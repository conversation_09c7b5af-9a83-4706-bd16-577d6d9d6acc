function _array_like_to_array(arr, len) {
    if (len == null || len > arr.length) len = arr.length;
    for(var i = 0, arr2 = new Array(len); i < len; i++)arr2[i] = arr[i];
    return arr2;
}
function _array_with_holes(arr) {
    if (Array.isArray(arr)) return arr;
}
function _array_without_holes(arr) {
    if (Array.isArray(arr)) return _array_like_to_array(arr);
}
function _assert_this_initialized(self) {
    if (self === void 0) {
        throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
    }
    return self;
}
function _class_call_check(instance, Constructor) {
    if (!(instance instanceof Constructor)) {
        throw new TypeError("Cannot call a class as a function");
    }
}
function _defineProperties(target, props) {
    for(var i = 0; i < props.length; i++){
        var descriptor = props[i];
        descriptor.enumerable = descriptor.enumerable || false;
        descriptor.configurable = true;
        if ("value" in descriptor) descriptor.writable = true;
        Object.defineProperty(target, descriptor.key, descriptor);
    }
}
function _create_class(Constructor, protoProps, staticProps) {
    if (protoProps) _defineProperties(Constructor.prototype, protoProps);
    if (staticProps) _defineProperties(Constructor, staticProps);
    return Constructor;
}
function _define_property(obj, key, value) {
    if (key in obj) {
        Object.defineProperty(obj, key, {
            value: value,
            enumerable: true,
            configurable: true,
            writable: true
        });
    } else {
        obj[key] = value;
    }
    return obj;
}
function _get_prototype_of(o) {
    _get_prototype_of = Object.setPrototypeOf ? Object.getPrototypeOf : function getPrototypeOf(o) {
        return o.__proto__ || Object.getPrototypeOf(o);
    };
    return _get_prototype_of(o);
}
function _inherits(subClass, superClass) {
    if (typeof superClass !== "function" && superClass !== null) {
        throw new TypeError("Super expression must either be null or a function");
    }
    subClass.prototype = Object.create(superClass && superClass.prototype, {
        constructor: {
            value: subClass,
            writable: true,
            configurable: true
        }
    });
    if (superClass) _set_prototype_of(subClass, superClass);
}
function _instanceof(left, right) {
    if (right != null && typeof Symbol !== "undefined" && right[Symbol.hasInstance]) {
        return !!right[Symbol.hasInstance](left);
    } else {
        return left instanceof right;
    }
}
function _iterable_to_array(iter) {
    if (typeof Symbol !== "undefined" && iter[Symbol.iterator] != null || iter["@@iterator"] != null) return Array.from(iter);
}
function _iterable_to_array_limit(arr, i) {
    var _i = arr == null ? null : typeof Symbol !== "undefined" && arr[Symbol.iterator] || arr["@@iterator"];
    if (_i == null) return;
    var _arr = [];
    var _n = true;
    var _d = false;
    var _s, _e;
    try {
        for(_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true){
            _arr.push(_s.value);
            if (i && _arr.length === i) break;
        }
    } catch (err) {
        _d = true;
        _e = err;
    } finally{
        try {
            if (!_n && _i["return"] != null) _i["return"]();
        } finally{
            if (_d) throw _e;
        }
    }
    return _arr;
}
function _non_iterable_rest() {
    throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}
function _non_iterable_spread() {
    throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}
function _possible_constructor_return(self, call) {
    if (call && (_type_of(call) === "object" || typeof call === "function")) {
        return call;
    }
    return _assert_this_initialized(self);
}
function _set_prototype_of(o, p) {
    _set_prototype_of = Object.setPrototypeOf || function setPrototypeOf(o, p) {
        o.__proto__ = p;
        return o;
    };
    return _set_prototype_of(o, p);
}
function _sliced_to_array(arr, i) {
    return _array_with_holes(arr) || _iterable_to_array_limit(arr, i) || _unsupported_iterable_to_array(arr, i) || _non_iterable_rest();
}
function _to_consumable_array(arr) {
    return _array_without_holes(arr) || _iterable_to_array(arr) || _unsupported_iterable_to_array(arr) || _non_iterable_spread();
}
function _type_of(obj) {
    "@swc/helpers - typeof";
    return obj && typeof Symbol !== "undefined" && obj.constructor === Symbol ? "symbol" : typeof obj;
}
function _unsupported_iterable_to_array(o, minLen) {
    if (!o) return;
    if (typeof o === "string") return _array_like_to_array(o, minLen);
    var n = Object.prototype.toString.call(o).slice(8, -1);
    if (n === "Object" && o.constructor) n = o.constructor.name;
    if (n === "Map" || n === "Set") return Array.from(n);
    if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _array_like_to_array(o, minLen);
}
function _is_native_reflect_construct() {
    if (typeof Reflect === "undefined" || !Reflect.construct) return false;
    if (Reflect.construct.sham) return false;
    if (typeof Proxy === "function") return true;
    try {
        Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function() {}));
        return true;
    } catch (e) {
        return false;
    }
}
function _create_super(Derived) {
    var hasNativeReflectConstruct = _is_native_reflect_construct();
    return function _createSuperInternal() {
        var Super = _get_prototype_of(Derived), result;
        if (hasNativeReflectConstruct) {
            var NewTarget = _get_prototype_of(this).constructor;
            result = Reflect.construct(Super, arguments, NewTarget);
        } else {
            result = Super.apply(this, arguments);
        }
        return _possible_constructor_return(this, result);
    };
}
import { CONTAINER, CONTEXT_ACTIONS, Current, ON_HIDE, ON_READY, ON_SHOW, _raf, addLeadingSlash, document$1, eventCenter, eventHandler, getOnHideEventKey, getOnReadyEventKey, getOnShowEventKey, getPageInstance, getPath, incrementId, injectPageInstance, removePageInstance, safeExecute, window$1 } from "./chunk-KTDWUN4J.js";
import { EMPTY_OBJ, ensure, hooks, isArray, isFunction, isUndefined } from "./chunk-VRMIYLYE.js";
// node_modules/@tarojs/plugin-framework-react/dist/runtime.js
var reactMeta = {
    PageContext: EMPTY_OBJ,
    R: EMPTY_OBJ
};
var HOOKS_APP_ID = "taro-app";
function isClassComponent(R, component) {
    var _a;
    var prototype = component.prototype;
    if ((_a = component.displayName) === null || _a === void 0 ? void 0 : _a.includes("Connect")) return false;
    return isFunction(component.render) || !!(prototype === null || prototype === void 0 ? void 0 : prototype.isReactComponent) || _instanceof(prototype, R.Component);
}
function ensureIsArray(item) {
    if (isArray(item)) {
        return item;
    } else {
        return item ? [
            item
        ] : [];
    }
}
function setDefaultDescriptor(obj) {
    obj.writable = true;
    obj.enumerable = true;
    return obj;
}
function setRouterParams(options) {
    Current.router = Object.assign({
        params: options === null || options === void 0 ? void 0 : options.query
    }, options);
}
var createTaroHook = function(lifecycle) {
    return function(fn) {
        var React = reactMeta.R, PageContext = reactMeta.PageContext;
        var id = React.useContext(PageContext) || HOOKS_APP_ID;
        var instRef = React.useRef();
        var fnRef = React.useRef(fn);
        if (fnRef.current !== fn) fnRef.current = fn;
        React.useLayoutEffect(function() {
            var _fnRef;
            var inst = instRef.current = getPageInstance(id);
            var first = false;
            if (!inst) {
                first = true;
                instRef.current = /* @__PURE__ */ Object.create(null);
                inst = instRef.current;
            }
            var callback = function() {
                for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){
                    args[_key] = arguments[_key];
                }
                return (_fnRef = fnRef).current.apply(_fnRef, _to_consumable_array(args));
            };
            if (isFunction(inst[lifecycle])) {
                inst[lifecycle] = [
                    inst[lifecycle],
                    callback
                ];
            } else {
                inst[lifecycle] = _to_consumable_array(inst[lifecycle] || []).concat([
                    callback
                ]);
            }
            if (first) {
                injectPageInstance(inst, id);
            }
            return function() {
                var inst2 = instRef.current;
                if (!inst2) return;
                var list = inst2[lifecycle];
                if (list === callback) {
                    inst2[lifecycle] = void 0;
                } else if (isArray(list)) {
                    inst2[lifecycle] = list.filter(function(item) {
                        return item !== callback;
                    });
                }
                instRef.current = void 0;
            };
        }, []);
    };
};
var useDidHide = createTaroHook("componentDidHide");
var useDidShow = createTaroHook("componentDidShow");
var useError = createTaroHook("onError");
var useUnhandledRejection = createTaroHook("onUnhandledRejection");
var useLaunch = createTaroHook("onLaunch");
var usePageNotFound = createTaroHook("onPageNotFound");
var useLoad = createTaroHook("onLoad");
var usePageScroll = createTaroHook("onPageScroll");
var usePullDownRefresh = createTaroHook("onPullDownRefresh");
var usePullIntercept = createTaroHook("onPullIntercept");
var useReachBottom = createTaroHook("onReachBottom");
var useResize = createTaroHook("onResize");
var useUnload = createTaroHook("onUnload");
var useAddToFavorites = createTaroHook("onAddToFavorites");
var useOptionMenuClick = createTaroHook("onOptionMenuClick");
var useSaveExitState = createTaroHook("onSaveExitState");
var useShareAppMessage = createTaroHook("onShareAppMessage");
var useShareTimeline = createTaroHook("onShareTimeline");
var useTitleClick = createTaroHook("onTitleClick");
var useReady = createTaroHook("onReady");
var useRouter = function() {
    var dynamic = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;
    var React = reactMeta.R;
    return dynamic ? Current.router : React.useMemo(function() {
        return Current.router;
    }, []);
};
var useTabItemTap = createTaroHook("onTabItemTap");
var useScope = function() {
    return void 0;
};
var taroHooks = Object.freeze({
    __proto__: null,
    useAddToFavorites: useAddToFavorites,
    useDidHide: useDidHide,
    useDidShow: useDidShow,
    useError: useError,
    useLaunch: useLaunch,
    useLoad: useLoad,
    useOptionMenuClick: useOptionMenuClick,
    usePageNotFound: usePageNotFound,
    usePageScroll: usePageScroll,
    usePullDownRefresh: usePullDownRefresh,
    usePullIntercept: usePullIntercept,
    useReachBottom: useReachBottom,
    useReady: useReady,
    useResize: useResize,
    useRouter: useRouter,
    useSaveExitState: useSaveExitState,
    useScope: useScope,
    useShareAppMessage: useShareAppMessage,
    useShareTimeline: useShareTimeline,
    useTabItemTap: useTabItemTap,
    useTitleClick: useTitleClick,
    useUnhandledRejection: useUnhandledRejection,
    useUnload: useUnload
});
var h$1;
var ReactDOM$1;
var Fragment;
var pageKeyId = incrementId();
function setReconciler(ReactDOM2) {
    hooks.tap("getLifecycle", function(instance, lifecycle) {
        lifecycle = lifecycle.replace(/^on(Show|Hide)$/, "componentDid$1");
        return instance[lifecycle];
    });
    hooks.tap("modifyMpEvent", function(event) {
        Object.defineProperty(event, "type", {
            value: event.type.replace(/-/g, "")
        });
    });
    hooks.tap("batchedEventUpdates", function(cb) {
        ReactDOM2.unstable_batchedUpdates(cb);
    });
    hooks.tap("mergePageInstance", function(prev, next) {
        if (!prev || !next) return;
        if ("constructor" in prev) return;
        Object.keys(prev).forEach(function(item) {
            var prevList = prev[item];
            var nextList = ensureIsArray(next[item]);
            next[item] = nextList.concat(prevList);
        });
    });
    if (true) {
        hooks.tap("createPullDownComponent", function(el, _, R, customWrapper) {
            var isReactComponent = isClassComponent(R, el);
            return R.forwardRef(function(props, ref) {
                var newProps = Object.assign({}, props);
                var refs = isReactComponent ? {
                    ref: ref
                } : {
                    forwardedRef: ref,
                    // 兼容 react-redux 7.20.1+
                    reactReduxForwardedRef: ref
                };
                return h$1(customWrapper || "taro-pull-to-refresh-core", null, h$1(el, Object.assign(Object.assign({}, newProps), refs)));
            });
        });
        hooks.tap("getDOMNode", function(inst) {
            if (!inst) {
                return document$1;
            } else if (_instanceof(inst, HTMLElement)) {
                return inst;
            } else if (inst.$taroPath) {
                var el = document$1.getElementById(inst.$taroPath);
                return el !== null && el !== void 0 ? el : document$1;
            }
        });
    }
}
function connectReactPage(R, id) {
    return function(Page) {
        var isReactComponent = isClassComponent(R, Page);
        var inject = function(node) {
            return node && injectPageInstance(node, id);
        };
        var refs = isReactComponent ? {
            ref: inject
        } : {
            forwardedRef: inject,
            // 兼容 react-redux 7.20.1+
            reactReduxForwardedRef: inject
        };
        if (reactMeta.PageContext === EMPTY_OBJ) {
            reactMeta.PageContext = R.createContext("");
        }
        return /*#__PURE__*/ function(_R_Component) {
            "use strict";
            _inherits(PageWrapper, _R_Component);
            var _super = _create_super(PageWrapper);
            function PageWrapper() {
                _class_call_check(this, PageWrapper);
                var _this;
                _this = _super.call.apply(_super, [
                    this
                ].concat(Array.prototype.slice.call(arguments)));
                _this.state = {
                    hasError: false
                };
                return _this;
            }
            _create_class(PageWrapper, [
                {
                    // React 16 uncaught error 会导致整个应用 crash，
                    // 目前把错误缩小到页面
                    key: "componentDidCatch",
                    value: function componentDidCatch(error, info) {
                        if (true) {
                            console.warn(error);
                            console.error(info.componentStack);
                        }
                    }
                },
                {
                    key: "render",
                    value: function render() {
                        var children = this.state.hasError ? [] : h$1(reactMeta.PageContext.Provider, {
                            value: id
                        }, h$1(Page, Object.assign(Object.assign({}, this.props), refs)));
                        if (true) {
                            return h$1("div", {
                                id: id,
                                className: "taro_page"
                            }, children);
                        } else {
                            return h$1("root", {
                                id: id
                            }, children);
                        }
                    }
                }
            ], [
                {
                    key: "getDerivedStateFromError",
                    value: function getDerivedStateFromError(error) {
                        var _a, _b;
                        (_b = (_a = Current.app) === null || _a === void 0 ? void 0 : _a.onError) === null || _b === void 0 ? void 0 : _b.call(_a, error.message + error.stack);
                        return {
                            hasError: true
                        };
                    }
                }
            ]);
            return PageWrapper;
        }(R.Component);
    };
}
function createReactApp(App, react, dom, config) {
    if (true) {
        ensure(!!dom, "构建 React/Nerv 项目请把 process.env.FRAMEWORK 设置为 'react'/'preact'/'nerv' ");
    }
    reactMeta.R = react;
    h$1 = react.createElement;
    ReactDOM$1 = dom;
    Fragment = react.Fragment;
    var appInstanceRef = react.createRef();
    var isReactComponent = isClassComponent(react, App);
    var appWrapper;
    var appWrapperResolver;
    var appWrapperPromise = new Promise(function(resolve) {
        return appWrapperResolver = resolve;
    });
    setReconciler(ReactDOM$1);
    function getAppInstance() {
        return appInstanceRef.current;
    }
    function waitAppWrapper(cb) {
        appWrapperPromise.then(function() {
            return cb();
        });
    }
    function renderReactRoot() {
        var _a, _b;
        var appId = (config === null || config === void 0 ? void 0 : config.appId) || "app";
        var container = document$1.getElementById(appId);
        if (container == null) {
            var appContainer = document$1.getElementById(CONTAINER);
            container = document$1.createElement(appId);
            container.id = appId;
            appContainer === null || appContainer === void 0 ? void 0 : appContainer.appendChild(container);
        }
        if ((react.version || "").startsWith("18")) {
            var root = ReactDOM$1.createRoot(container);
            (_a = root.render) === null || _a === void 0 ? void 0 : _a.call(root, h$1(AppWrapper));
        } else {
            (_b = ReactDOM$1.render) === null || _b === void 0 ? void 0 : _b.call(ReactDOM$1, h$1(AppWrapper), container);
        }
    }
    var AppWrapper = /*#__PURE__*/ function(_react_Component) {
        "use strict";
        _inherits(AppWrapper, _react_Component);
        var _super = _create_super(AppWrapper);
        function AppWrapper(props) {
            _class_call_check(this, AppWrapper);
            var _this;
            _this = _super.call(this, props);
            _this.pages = [];
            _this.elements = [];
            appWrapper = _assert_this_initialized(_this);
            appWrapperResolver(_assert_this_initialized(_this));
            return _this;
        }
        _create_class(AppWrapper, [
            {
                key: "mount",
                value: function mount(pageComponent, id, cb) {
                    var pageWrapper = connectReactPage(react, id)(pageComponent);
                    var key = id + pageKeyId();
                    var page = function() {
                        return h$1(pageWrapper, {
                            key: key,
                            tid: id
                        });
                    };
                    this.pages.push(page);
                    this.forceUpdate(cb);
                }
            },
            {
                key: "unmount",
                value: function unmount(id, cb) {
                    var elements = this.elements;
                    var idx = elements.findIndex(function(item) {
                        return item.props.tid === id;
                    });
                    elements.splice(idx, 1);
                    this.forceUpdate(cb);
                }
            },
            {
                key: "render",
                value: function render() {
                    var _this = this, pages = _this.pages, elements = _this.elements;
                    while(pages.length > 0){
                        var page = pages.pop();
                        elements.push(page());
                    }
                    var props = null;
                    if (isReactComponent) {
                        props = {
                            ref: appInstanceRef
                        };
                    }
                    return h$1(App, props, true ? h$1(Fragment !== null && Fragment !== void 0 ? Fragment : "div", null, elements.slice()) : elements.slice());
                }
            }
        ]);
        return AppWrapper;
    }(react.Component);
    if (false) {
        renderReactRoot();
    }
    var _hooks_call_app = _sliced_to_array(hooks.call("getMiniLifecycleImpl").app, 3), ONLAUNCH = _hooks_call_app[0], ONSHOW = _hooks_call_app[1], ONHIDE = _hooks_call_app[2];
    var _obj;
    var appObj = Object.create({
        render: function render(cb) {
            appWrapper.forceUpdate(cb);
        },
        mount: function mount(component, id, cb) {
            if (appWrapper) {
                appWrapper.mount(component, id, cb);
            } else {
                appWrapperPromise.then(function(appWrapper2) {
                    return appWrapper2.mount(component, id, cb);
                });
            }
        },
        unmount: function unmount(id, cb) {
            if (appWrapper) {
                appWrapper.unmount(id, cb);
            } else {
                appWrapperPromise.then(function(appWrapper2) {
                    return appWrapper2.unmount(id, cb);
                });
            }
        }
    }, (_obj = {
        config: setDefaultDescriptor({
            configurable: true,
            value: config
        })
    }, _define_property(_obj, ONLAUNCH, setDefaultDescriptor({
        value: function value(options) {
            var _this = this;
            setRouterParams(options);
            if (true) {
                renderReactRoot();
            }
            var onLaunch = function() {
                var _a;
                var app = getAppInstance();
                _this.$app = app;
                if (app) {
                    if (app.taroGlobalData) {
                        var globalData = app.taroGlobalData;
                        var keys = Object.keys(globalData);
                        var descriptors = Object.getOwnPropertyDescriptors(globalData);
                        keys.forEach(function(key) {
                            Object.defineProperty(_this, key, {
                                configurable: true,
                                enumerable: true,
                                get: function get() {
                                    return globalData[key];
                                },
                                set: function set(value) {
                                    globalData[key] = value;
                                }
                            });
                        });
                        Object.defineProperties(_this, descriptors);
                    }
                    (_a = app.onLaunch) === null || _a === void 0 ? void 0 : _a.call(app, options);
                }
                triggerAppHook("onLaunch", options);
            };
            waitAppWrapper(onLaunch);
        }
    })), _define_property(_obj, ONSHOW, setDefaultDescriptor({
        value: function value(options) {
            setRouterParams(options);
            var onShow = function() {
                var _a;
                var app = getAppInstance();
                (_a = app === null || app === void 0 ? void 0 : app.componentDidShow) === null || _a === void 0 ? void 0 : _a.call(app, options);
                triggerAppHook("onShow", options);
            };
            waitAppWrapper(onShow);
        }
    })), _define_property(_obj, ONHIDE, setDefaultDescriptor({
        value: function value() {
            var onHide = function() {
                var _a;
                var app = getAppInstance();
                (_a = app === null || app === void 0 ? void 0 : app.componentDidHide) === null || _a === void 0 ? void 0 : _a.call(app);
                triggerAppHook("onHide");
            };
            waitAppWrapper(onHide);
        }
    })), _define_property(_obj, "onError", setDefaultDescriptor({
        value: function value(error) {
            var onError = function() {
                var _a;
                var app = getAppInstance();
                (_a = app === null || app === void 0 ? void 0 : app.onError) === null || _a === void 0 ? void 0 : _a.call(app, error);
                triggerAppHook("onError", error);
                if (error === null || error === void 0 ? void 0 : error.includes("Minified React error")) {
                    console.warn("React 出现报错，请打开编译配置 mini.debugReact 查看报错详情：https://docs.taro.zone/docs/config-detail#minidebugreact");
                }
            };
            waitAppWrapper(onError);
        }
    })), _define_property(_obj, "onUnhandledRejection", setDefaultDescriptor({
        value: function value(res) {
            var onUnhandledRejection = function() {
                var _a;
                var app = getAppInstance();
                (_a = app === null || app === void 0 ? void 0 : app.onUnhandledRejection) === null || _a === void 0 ? void 0 : _a.call(app, res);
                triggerAppHook("onUnhandledRejection", res);
            };
            waitAppWrapper(onUnhandledRejection);
        }
    })), _define_property(_obj, "onPageNotFound", setDefaultDescriptor({
        value: function value(res) {
            var onPageNotFound = function() {
                var _a;
                var app = getAppInstance();
                (_a = app === null || app === void 0 ? void 0 : app.onPageNotFound) === null || _a === void 0 ? void 0 : _a.call(app, res);
                triggerAppHook("onPageNotFound", res);
            };
            waitAppWrapper(onPageNotFound);
        }
    })), _obj));
    function triggerAppHook(lifecycle) {
        for(var _len = arguments.length, option = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){
            option[_key - 1] = arguments[_key];
        }
        var instance = getPageInstance(HOOKS_APP_ID);
        if (instance) {
            var app = getAppInstance();
            var func = hooks.call("getLifecycle", instance, lifecycle);
            if (Array.isArray(func)) {
                func.forEach(function(cb) {
                    return cb.apply(app, option);
                });
            }
        }
    }
    Current.app = appObj;
    return appObj;
}
var getNativeCompId = incrementId();
var h;
var ReactDOM;
var nativeComponentApp;
function initNativeComponentEntry(params) {
    var _a;
    var R = params.R, ReactDOM2 = params.ReactDOM, cb = params.cb, _params_isDefaultEntryDom = params.isDefaultEntryDom, isDefaultEntryDom = _params_isDefaultEntryDom === void 0 ? true : _params_isDefaultEntryDom;
    var NativeComponentWrapper = /*#__PURE__*/ function(_R_Component) {
        "use strict";
        _inherits(NativeComponentWrapper, _R_Component);
        var _super = _create_super(NativeComponentWrapper);
        function NativeComponentWrapper() {
            _class_call_check(this, NativeComponentWrapper);
            var _this;
            _this = _super.call.apply(_super, [
                this
            ].concat(Array.prototype.slice.call(arguments)));
            _this.root = R.createRef();
            _this.ctx = _this.props.getCtx();
            return _this;
        }
        _create_class(NativeComponentWrapper, [
            {
                key: "componentDidMount",
                value: function componentDidMount() {
                    this.ctx.component = this;
                    var rootElement = this.root.current;
                    rootElement.ctx = this.ctx;
                    rootElement.performUpdate(true);
                }
            },
            {
                key: "render",
                value: function render() {
                    return h("root", {
                        ref: this.root,
                        id: this.props.compId
                    }, this.props.renderComponent(this.ctx));
                }
            }
        ]);
        return NativeComponentWrapper;
    }(R.Component);
    var Entry = /*#__PURE__*/ function(_R_Component) {
        "use strict";
        _inherits(Entry, _R_Component);
        var _super = _create_super(Entry);
        function Entry() {
            _class_call_check(this, Entry);
            var _this;
            _this = _super.call.apply(_super, [
                this
            ].concat(Array.prototype.slice.call(arguments)));
            _this.state = {
                components: []
            };
            return _this;
        }
        _create_class(Entry, [
            {
                key: "componentDidMount",
                value: function componentDidMount() {
                    if (isDefaultEntryDom) {
                        Current.app = this;
                    } else {
                        nativeComponentApp = this;
                    }
                    cb && cb();
                }
            },
            {
                key: "mount",
                value: function mount(Component, compId, getCtx, cb2) {
                    var isReactComponent = isClassComponent(R, Component);
                    var inject = function(node) {
                        return node && injectPageInstance(node, compId);
                    };
                    var refs = isReactComponent ? {
                        ref: inject
                    } : {
                        forwardedRef: inject,
                        reactReduxForwardedRef: inject
                    };
                    if (reactMeta.PageContext === EMPTY_OBJ) {
                        reactMeta.PageContext = R.createContext("");
                    }
                    var item = {
                        compId: compId,
                        element: h(NativeComponentWrapper, {
                            key: compId,
                            compId: compId,
                            getCtx: getCtx,
                            renderComponent: function renderComponent(ctx) {
                                return h(reactMeta.PageContext.Provider, {
                                    value: compId
                                }, h(Component, Object.assign(Object.assign(Object.assign({}, (ctx.data || (ctx.data = {})).props), refs), {
                                    $scope: ctx
                                })));
                            }
                        })
                    };
                    this.setState({
                        components: _to_consumable_array(this.state.components).concat([
                            item
                        ])
                    }, function() {
                        return cb2 && cb2();
                    });
                }
            },
            {
                key: "unmount",
                value: function unmount(compId, cb2) {
                    var components = this.state.components;
                    var index = components.findIndex(function(item) {
                        return item.compId === compId;
                    });
                    var next = _to_consumable_array(components.slice(0, index)).concat(_to_consumable_array(components.slice(index + 1)));
                    this.setState({
                        components: next
                    }, function() {
                        removePageInstance(compId);
                        cb2 && cb2();
                    });
                }
            },
            {
                key: "render",
                value: function render() {
                    var components = this.state.components;
                    return components.map(function(param) {
                        var element = param.element;
                        return element;
                    });
                }
            }
        ]);
        return Entry;
    }(R.Component);
    setReconciler(ReactDOM2);
    var app = document$1.getElementById("app");
    if (!isDefaultEntryDom && !nativeComponentApp) {
        var nativeApp = document$1.createElement("nativeComponent");
        (_a = app === null || app === void 0 ? void 0 : app.parentNode) === null || _a === void 0 ? void 0 : _a.appendChild(nativeApp);
        app = nativeApp;
    }
    ReactDOM2.render(h(Entry, {}), app);
}
function createNativePageConfig(Component, pageName, data, react, reactdom, pageConfig) {
    reactMeta.R = react;
    h = react.createElement;
    ReactDOM = reactdom;
    setReconciler(ReactDOM);
    var _hooks_call_page = _sliced_to_array(hooks.call("getMiniLifecycleImpl").page, 7), ONLOAD = _hooks_call_page[0], ONUNLOAD = _hooks_call_page[1], ONREADY = _hooks_call_page[2], ONSHOW = _hooks_call_page[3], ONHIDE = _hooks_call_page[4], LIFECYCLES = _hooks_call_page[5], SIDE_EFFECT_LIFECYCLES = _hooks_call_page[6];
    var unmounting = false;
    var prepareMountList = [];
    var pageElement = null;
    var loadResolver;
    var hasLoaded;
    var id = pageName !== null && pageName !== void 0 ? pageName : "taro_page_".concat(getNativeCompId());
    function setCurrentRouter(page) {
        var router = page.route || page.__route__ || page.$taroPath;
        Current.router = {
            params: page.$taroParams,
            path: addLeadingSlash(router),
            $taroPath: page.$taroPath,
            onReady: getOnReadyEventKey(id),
            onShow: getOnShowEventKey(id),
            onHide: getOnHideEventKey(id)
        };
        if (!isUndefined(page.exitState)) {
            Current.router.exitState = page.exitState;
        }
    }
    var _obj;
    var pageObj = (_obj = {
        options: pageConfig
    }, _define_property(_obj, ONLOAD, function() {
        var options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {}, cb = arguments.length > 1 ? arguments[1] : void 0;
        var _this = this;
        hasLoaded = new Promise(function(resolve) {
            loadResolver = resolve;
        });
        Current.page = this;
        this.config = pageConfig || {};
        var uniqueOptions = Object.assign({}, options, {
            $taroTimestamp: Date.now()
        });
        var $taroPath = this.$taroPath = getPath(id, uniqueOptions);
        if (this.$taroParams == null) {
            this.$taroParams = uniqueOptions;
        }
        setCurrentRouter(this);
        window$1.trigger(CONTEXT_ACTIONS.INIT, $taroPath);
        var mountCallback = function() {
            pageElement = document$1.getElementById($taroPath);
            ensure(pageElement !== null, "没有找到页面实例。");
            safeExecute($taroPath, ONLOAD, _this.$taroParams);
            loadResolver();
            pageElement.ctx = _this;
            pageElement.performUpdate(true, cb);
        };
        var mount = function() {
            if (!Current.app) {
                initNativeComponentEntry({
                    R: react,
                    ReactDOM: ReactDOM,
                    cb: function() {
                        Current.app.mount(Component, $taroPath, function() {
                            return _this;
                        }, mountCallback);
                    }
                });
            } else {
                Current.app.mount(Component, $taroPath, function() {
                    return _this;
                }, mountCallback);
            }
        };
        if (unmounting) {
            prepareMountList.push(mount);
        } else {
            mount();
        }
    }), _define_property(_obj, ONUNLOAD, function() {
        var $taroPath = this.$taroPath;
        window$1.trigger(CONTEXT_ACTIONS.DESTORY, $taroPath);
        safeExecute($taroPath, ONUNLOAD);
        resetCurrent();
        unmounting = true;
        Current.app.unmount($taroPath, function() {
            unmounting = false;
            removePageInstance($taroPath);
            if (pageElement) {
                pageElement.ctx = null;
                pageElement = null;
            }
            if (prepareMountList.length) {
                prepareMountList.forEach(function(fn) {
                    return fn();
                });
                prepareMountList = [];
            }
        });
    }), _define_property(_obj, ONREADY, function() {
        var _this = this;
        hasLoaded.then(function() {
            safeExecute(_this.$taroPath, ON_READY);
            _raf(function() {
                return eventCenter.trigger(getOnReadyEventKey(id));
            });
            _this.onReady.called = true;
        });
    }), _define_property(_obj, ONSHOW, function() {
        var options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
        var _this = this;
        hasLoaded.then(function() {
            Current.page = _this;
            setCurrentRouter(_this);
            window$1.trigger(CONTEXT_ACTIONS.RECOVER, _this.$taroPath);
            safeExecute(_this.$taroPath, ON_SHOW, options);
            _raf(function() {
                return eventCenter.trigger(getOnShowEventKey(id));
            });
        });
    }), _define_property(_obj, ONHIDE, function() {
        window$1.trigger(CONTEXT_ACTIONS.RESTORE, this.$taroPath);
        if (Current.page === this) {
            Current.page = null;
            Current.router = null;
        }
        safeExecute(this.$taroPath, ON_HIDE);
        eventCenter.trigger(getOnHideEventKey(id));
    }), _obj);
    function resetCurrent() {
        Current.page = null;
        Current.router = null;
    }
    LIFECYCLES.forEach(function(lifecycle) {
        pageObj[lifecycle] = function() {
            return safeExecute.apply(void 0, [
                this.$taroPath,
                lifecycle
            ].concat(Array.prototype.slice.call(arguments)));
        };
    });
    SIDE_EFFECT_LIFECYCLES.forEach(function(lifecycle) {
        var _a;
        if (Component[lifecycle] || ((_a = Component.prototype) === null || _a === void 0 ? void 0 : _a[lifecycle]) || Component[lifecycle.replace(/^on/, "enable")]) {
            pageObj[lifecycle] = function() {
                for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){
                    args[_key] = arguments[_key];
                }
                var _a2;
                var target = (_a2 = args[0]) === null || _a2 === void 0 ? void 0 : _a2.target;
                if (target === null || target === void 0 ? void 0 : target.id) {
                    var id2 = target.id;
                    var element = document$1.getElementById(id2);
                    if (element) {
                        target.dataset = element.dataset;
                    }
                }
                return safeExecute.apply(void 0, [
                    this.$taroPath,
                    lifecycle
                ].concat(_to_consumable_array(args)));
            };
        }
    });
    pageObj.eh = eventHandler;
    if (!isUndefined(data)) {
        pageObj.data = data;
    }
    hooks.call("modifyPageObject", pageObj);
    return pageObj;
}
function createH5NativeComponentConfig(Component, react, reactdom) {
    reactMeta.R = react;
    h = react.createElement;
    ReactDOM = reactdom;
    setReconciler(ReactDOM);
    return Component;
}
function createNativeComponentConfig(Component, react, reactdom, componentConfig) {
    var _a, _b;
    reactMeta.R = react;
    h = react.createElement;
    ReactDOM = reactdom;
    setReconciler(ReactDOM);
    var isNewBlended = componentConfig.isNewBlended;
    var componentObj = {
        options: componentConfig,
        properties: {
            props: {
                type: null,
                value: null,
                observer: function observer(_newVal, oldVal) {
                    var _a2, _b2, _c, _d;
                    if (false) {
                        var inst = document$1.getElementById(this.id);
                        if (((_b2 = (_a2 = this.component) === null || _a2 === void 0 ? void 0 : _a2.ctx) === null || _b2 === void 0 ? void 0 : _b2.data) && inst) {
                            this.component.ctx.data.props = (_c = inst === null || inst === void 0 ? void 0 : inst.props) === null || _c === void 0 ? void 0 : _c.props;
                        }
                    }
                    oldVal && ((_d = this.component) === null || _d === void 0 ? void 0 : _d.forceUpdate());
                }
            }
        },
        created: function created() {
            var _a2, _b2;
            if (false) {
                var inst = document$1.getElementById(this.id);
                if (((_a2 = this.data) === null || _a2 === void 0 ? void 0 : _a2.props) && inst) {
                    this.data.props = ((_b2 = inst.props) === null || _b2 === void 0 ? void 0 : _b2.props) || {};
                }
            }
            var app = isNewBlended ? nativeComponentApp : Current.app;
            if (!app) {
                initNativeComponentEntry({
                    R: react,
                    ReactDOM: ReactDOM,
                    isDefaultEntryDom: !isNewBlended
                });
            }
        },
        attached: function attached() {
            var _this = this;
            var compId = this.compId = getNativeCompId();
            setCurrent(compId);
            this.config = componentConfig;
            var app = isNewBlended ? nativeComponentApp : Current.app;
            app.mount(Component, compId, function() {
                return _this;
            }, function() {
                var instance = getPageInstance(compId);
                if (instance && instance.node) {
                    var el = document$1.getElementById(instance.node.uid);
                    if (el) {
                        el.ctx = _this;
                    }
                }
            });
        },
        ready: function ready() {
            safeExecute(this.compId, "onReady");
        },
        detached: function detached() {
            resetCurrent();
            var app = isNewBlended ? nativeComponentApp : Current.app;
            app.unmount(this.compId);
        },
        pageLifetimes: {
            show: function show(options) {
                safeExecute(this.compId, "onShow", options);
            },
            hide: function hide() {
                safeExecute(this.compId, "onHide");
            }
        },
        methods: {
            eh: eventHandler,
            onLoad: function onLoad(options) {
                safeExecute(this.compId, "onLoad", options);
            },
            onUnload: function onUnload() {
                safeExecute(this.compId, "onUnload");
            }
        }
    };
    function resetCurrent() {
        Current.page = null;
        Current.router = null;
    }
    if (Component.onShareAppMessage || ((_a = Component.prototype) === null || _a === void 0 ? void 0 : _a.onShareAppMessage) || Component.enableShareAppMessage) {
        componentObj.methods.onShareAppMessage = function(options) {
            var target = options === null || options === void 0 ? void 0 : options.target;
            if (target) {
                var id = target.id;
                var element = document$1.getElementById(id);
                if (element) {
                    target.dataset = element.dataset;
                }
            }
            return safeExecute(this.compId, "onShareAppMessage", options);
        };
    }
    if (Component.onShareTimeline || ((_b = Component.prototype) === null || _b === void 0 ? void 0 : _b.onShareTimeline) || Component.enableShareTimeline) {
        componentObj.methods.onShareTimeline = function() {
            return safeExecute(this.compId, "onShareTimeline");
        };
    }
    if (false) {
        componentObj.onInit = componentObj.created;
        componentObj.didMount = componentObj.attached;
        componentObj.didUpdate = function() {
            var _a2, _b2;
            this.data.props = this.props.props;
            (_b2 = (_a2 = this === null || this === void 0 ? void 0 : this.component) === null || _a2 === void 0 ? void 0 : _a2.forceUpdate) === null || _b2 === void 0 ? void 0 : _b2.call(_a2);
        };
        componentObj.didUnmount = componentObj.detached;
    }
    return componentObj;
}
function setCurrent(compId) {
    if (!getCurrentPages || typeof getCurrentPages !== "function") return;
    var pages = getCurrentPages();
    var currentPage = pages[pages.length - 1];
    if (Current.page === currentPage) return;
    Current.page = currentPage;
    var route = currentPage.route || currentPage.__route__;
    var router = {
        params: currentPage.options || {},
        path: addLeadingSlash(route),
        $taroPath: compId,
        onReady: "",
        onHide: "",
        onShow: ""
    };
    Current.router = router;
    if (!currentPage.options) {
        Object.defineProperty(currentPage, "options", {
            enumerable: true,
            configurable: true,
            get: function get() {
                return this._optionsValue;
            },
            set: function set(value) {
                router.params = value;
                this._optionsValue = value;
            }
        });
    }
}
hooks.tap("initNativeApi", function(taro) {
    for(var hook in taroHooks){
        taro[hook] = taroHooks[hook];
    }
});
if (false) {
    var options = null.options;
    var oldVNodeHook = options.vnode;
    var oldDiffedHook = options.diffed;
    options.vnode = function(vnode) {
        var type = vnode.type, props = vnode.props;
        var normalizedProps = props;
        if (typeof type === "string") {
            normalizedProps = {};
            for(var i in props){
                var value = props[i];
                if (/^on/.test(i)) {
                    i = i.toLowerCase();
                }
                if (type === "map" && i === "onregionchange") {
                    normalizedProps.onbegin = value;
                    normalizedProps.onend = value;
                    continue;
                }
                normalizedProps[i] = value;
            }
            vnode.props = normalizedProps;
        }
        if (oldVNodeHook) oldVNodeHook(vnode);
    };
    options.diffed = function(newVNode) {
        var _a;
        var domProp = Object.keys(newVNode).find(function(k) {
            var _a2;
            return (_a2 = newVNode[k]) === null || _a2 === void 0 ? void 0 : _a2.setAttribute;
        });
        var dom = domProp ? newVNode[domProp] : null;
        var newVNodeProps = newVNode.props;
        if (dom) {
            for(var propName in newVNodeProps){
                var propValue = newVNodeProps[propName];
                if (propValue === false && ((_a = dom.props) === null || _a === void 0 ? void 0 : _a[propName]) === void 0) {
                    dom.setAttribute(propName, propValue);
                }
            }
        }
        if (oldDiffedHook) oldDiffedHook(newVNode);
    };
    hooks.tap("modifyMpEvent", function(e) {
        var type = e.type;
        if (type === "tap") {
            e.type = "click";
        } else if (type === "focus") {
            e.type = "focusin";
        } else if (type === "blur") {
            e.type = "focusout";
        }
    });
}
export { useDidHide, useDidShow, useError, useUnhandledRejection, useLaunch, usePageNotFound, useLoad, usePageScroll, usePullDownRefresh, usePullIntercept, useReachBottom, useResize, useUnload, useAddToFavorites, useOptionMenuClick, useSaveExitState, useShareAppMessage, useShareTimeline, useTitleClick, useReady, useRouter, useTabItemTap, useScope, setReconciler, connectReactPage, createReactApp, createNativePageConfig, createH5NativeComponentConfig, createNativeComponentConfig };
