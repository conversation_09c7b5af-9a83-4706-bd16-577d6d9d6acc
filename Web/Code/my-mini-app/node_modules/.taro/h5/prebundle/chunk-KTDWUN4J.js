function _array_like_to_array(arr, len) {
    if (len == null || len > arr.length) len = arr.length;
    for(var i = 0, arr2 = new Array(len); i < len; i++)arr2[i] = arr[i];
    return arr2;
}
function _array_with_holes(arr) {
    if (Array.isArray(arr)) return arr;
}
function _array_without_holes(arr) {
    if (Array.isArray(arr)) return _array_like_to_array(arr);
}
function _assert_this_initialized(self) {
    if (self === void 0) {
        throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
    }
    return self;
}
function _class_call_check(instance, Constructor) {
    if (!(instance instanceof Constructor)) {
        throw new TypeError("Cannot call a class as a function");
    }
}
function _construct(Parent, args, Class) {
    if (_is_native_reflect_construct()) {
        _construct = Reflect.construct;
    } else {
        _construct = function construct(Parent, args, Class) {
            var a = [
                null
            ];
            a.push.apply(a, args);
            var Constructor = Function.bind.apply(Parent, a);
            var instance = new Constructor();
            if (Class) _set_prototype_of(instance, Class.prototype);
            return instance;
        };
    }
    return _construct.apply(null, arguments);
}
function _defineProperties(target, props) {
    for(var i = 0; i < props.length; i++){
        var descriptor = props[i];
        descriptor.enumerable = descriptor.enumerable || false;
        descriptor.configurable = true;
        if ("value" in descriptor) descriptor.writable = true;
        Object.defineProperty(target, descriptor.key, descriptor);
    }
}
function _create_class(Constructor, protoProps, staticProps) {
    if (protoProps) _defineProperties(Constructor.prototype, protoProps);
    if (staticProps) _defineProperties(Constructor, staticProps);
    return Constructor;
}
function _define_property(obj, key, value) {
    if (key in obj) {
        Object.defineProperty(obj, key, {
            value: value,
            enumerable: true,
            configurable: true,
            writable: true
        });
    } else {
        obj[key] = value;
    }
    return obj;
}
function _get(target, property, receiver) {
    if (typeof Reflect !== "undefined" && Reflect.get) {
        _get = Reflect.get;
    } else {
        _get = function get(target, property, receiver) {
            var base = _super_prop_base(target, property);
            if (!base) return;
            var desc = Object.getOwnPropertyDescriptor(base, property);
            if (desc.get) {
                return desc.get.call(receiver || target);
            }
            return desc.value;
        };
    }
    return _get(target, property, receiver || target);
}
function _get_prototype_of(o) {
    _get_prototype_of = Object.setPrototypeOf ? Object.getPrototypeOf : function getPrototypeOf(o) {
        return o.__proto__ || Object.getPrototypeOf(o);
    };
    return _get_prototype_of(o);
}
function _inherits(subClass, superClass) {
    if (typeof superClass !== "function" && superClass !== null) {
        throw new TypeError("Super expression must either be null or a function");
    }
    subClass.prototype = Object.create(superClass && superClass.prototype, {
        constructor: {
            value: subClass,
            writable: true,
            configurable: true
        }
    });
    if (superClass) _set_prototype_of(subClass, superClass);
}
function _instanceof(left, right) {
    if (right != null && typeof Symbol !== "undefined" && right[Symbol.hasInstance]) {
        return !!right[Symbol.hasInstance](left);
    } else {
        return left instanceof right;
    }
}
function _is_native_function(fn) {
    return Function.toString.call(fn).indexOf("[native code]") !== -1;
}
function _iterable_to_array(iter) {
    if (typeof Symbol !== "undefined" && iter[Symbol.iterator] != null || iter["@@iterator"] != null) return Array.from(iter);
}
function _iterable_to_array_limit(arr, i) {
    var _i = arr == null ? null : typeof Symbol !== "undefined" && arr[Symbol.iterator] || arr["@@iterator"];
    if (_i == null) return;
    var _arr = [];
    var _n = true;
    var _d = false;
    var _s, _e;
    try {
        for(_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true){
            _arr.push(_s.value);
            if (i && _arr.length === i) break;
        }
    } catch (err) {
        _d = true;
        _e = err;
    } finally{
        try {
            if (!_n && _i["return"] != null) _i["return"]();
        } finally{
            if (_d) throw _e;
        }
    }
    return _arr;
}
function _non_iterable_rest() {
    throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}
function _non_iterable_spread() {
    throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}
function _possible_constructor_return(self, call) {
    if (call && (_type_of(call) === "object" || typeof call === "function")) {
        return call;
    }
    return _assert_this_initialized(self);
}
function set(target, property, value, receiver) {
    if (typeof Reflect !== "undefined" && Reflect.set) {
        set = Reflect.set;
    } else {
        set = function set(target, property, value, receiver) {
            var base = _super_prop_base(target, property);
            var desc;
            if (base) {
                desc = Object.getOwnPropertyDescriptor(base, property);
                if (desc.set) {
                    desc.set.call(receiver, value);
                    return true;
                } else if (!desc.writable) {
                    return false;
                }
            }
            desc = Object.getOwnPropertyDescriptor(receiver, property);
            if (desc) {
                if (!desc.writable) {
                    return false;
                }
                desc.value = value;
                Object.defineProperty(receiver, property, desc);
            } else {
                _define_property(receiver, property, value);
            }
            return true;
        };
    }
    return set(target, property, value, receiver);
}
function _set(target, property, value, receiver, isStrict) {
    var s = set(target, property, value, receiver || target);
    if (!s && isStrict) {
        throw new Error("failed to set property");
    }
    return value;
}
function _set_prototype_of(o, p) {
    _set_prototype_of = Object.setPrototypeOf || function setPrototypeOf(o, p) {
        o.__proto__ = p;
        return o;
    };
    return _set_prototype_of(o, p);
}
function _sliced_to_array(arr, i) {
    return _array_with_holes(arr) || _iterable_to_array_limit(arr, i) || _unsupported_iterable_to_array(arr, i) || _non_iterable_rest();
}
function _super_prop_base(object, property) {
    while(!Object.prototype.hasOwnProperty.call(object, property)){
        object = _get_prototype_of(object);
        if (object === null) break;
    }
    return object;
}
function _to_array(arr) {
    return _array_with_holes(arr) || _iterable_to_array(arr) || _unsupported_iterable_to_array(arr) || _non_iterable_rest();
}
function _to_consumable_array(arr) {
    return _array_without_holes(arr) || _iterable_to_array(arr) || _unsupported_iterable_to_array(arr) || _non_iterable_spread();
}
function _type_of(obj) {
    "@swc/helpers - typeof";
    return obj && typeof Symbol !== "undefined" && obj.constructor === Symbol ? "symbol" : typeof obj;
}
function _unsupported_iterable_to_array(o, minLen) {
    if (!o) return;
    if (typeof o === "string") return _array_like_to_array(o, minLen);
    var n = Object.prototype.toString.call(o).slice(8, -1);
    if (n === "Object" && o.constructor) n = o.constructor.name;
    if (n === "Map" || n === "Set") return Array.from(n);
    if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _array_like_to_array(o, minLen);
}
function _wrap_native_super(Class) {
    var _cache = typeof Map === "function" ? new Map() : undefined;
    _wrap_native_super = function wrapNativeSuper(Class) {
        if (Class === null || !_is_native_function(Class)) return Class;
        if (typeof Class !== "function") {
            throw new TypeError("Super expression must either be null or a function");
        }
        if (typeof _cache !== "undefined") {
            if (_cache.has(Class)) return _cache.get(Class);
            _cache.set(Class, Wrapper);
        }
        function Wrapper() {
            return _construct(Class, arguments, _get_prototype_of(this).constructor);
        }
        Wrapper.prototype = Object.create(Class.prototype, {
            constructor: {
                value: Wrapper,
                enumerable: false,
                writable: true,
                configurable: true
            }
        });
        return _set_prototype_of(Wrapper, Class);
    };
    return _wrap_native_super(Class);
}
function _is_native_reflect_construct() {
    if (typeof Reflect === "undefined" || !Reflect.construct) return false;
    if (Reflect.construct.sham) return false;
    if (typeof Proxy === "function") return true;
    try {
        Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function() {}));
        return true;
    } catch (e) {
        return false;
    }
}
function _create_super(Derived) {
    var hasNativeReflectConstruct = _is_native_reflect_construct();
    return function _createSuperInternal() {
        var Super = _get_prototype_of(Derived), result;
        if (hasNativeReflectConstruct) {
            var NewTarget = _get_prototype_of(this).constructor;
            result = Reflect.construct(Super, arguments, NewTarget);
        } else {
            result = Super.apply(this, arguments);
        }
        return _possible_constructor_return(this, result);
    };
}
import { EMPTY_OBJ, EventChannel, Events, ensure, getComponentsAlias, hooks, internalComponents, isArray, isFunction, isNull, isNumber, isObject, isString, isUndefined, noop, toCamelCase, toDashed, warn } from "./chunk-VRMIYLYE.js";
// node_modules/tslib/tslib.es6.mjs
function __rest(s, e) {
    var t = {};
    for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function") for(var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++){
        if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
    }
    return t;
}
function __awaiter(thisArg, _arguments, P, generator) {
    function adopt(value) {
        return _instanceof(value, P) ? value : new P(function(resolve) {
            resolve(value);
        });
    }
    return new (P || (P = Promise))(function(resolve, reject) {
        function fulfilled(value) {
            try {
                step(generator.next(value));
            } catch (e) {
                reject(e);
            }
        }
        function rejected(value) {
            try {
                step(generator["throw"](value));
            } catch (e) {
                reject(e);
            }
        }
        function step(result) {
            result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
        }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
}
function __classPrivateFieldGet(receiver, state, kind, f) {
    if (kind === "a" && !f) throw new TypeError("Private accessor was defined without a getter");
    if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError("Cannot read private member from an object whose class did not declare it");
    return kind === "m" ? f : kind === "a" ? f.call(receiver) : f ? f.value : state.get(receiver);
}
function __classPrivateFieldSet(receiver, state, value, kind, f) {
    if (kind === "m") throw new TypeError("Private method is not writable");
    if (kind === "a" && !f) throw new TypeError("Private accessor was defined without a setter");
    if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError("Cannot write private member to an object whose class did not declare it");
    return kind === "a" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value), value;
}
// node_modules/@tarojs/runtime/dist/runtime.esm.js
var PROPERTY_THRESHOLD = 2046;
var TARO_RUNTIME = "Taro runtime";
var HOOKS_APP_ID = "taro-app";
var SET_DATA = "小程序 setData";
var PAGE_INIT = "页面初始化";
var ROOT_STR = "root";
var HTML = "html";
var HEAD = "head";
var BODY = "body";
var APP = "app";
var CONTAINER = "container";
var DOCUMENT_ELEMENT_NAME = "#document";
var DOCUMENT_FRAGMENT = "document-fragment";
var ID = "id";
var UID = "uid";
var CLASS = "class";
var STYLE = "style";
var FOCUS = "focus";
var VIEW = "view";
var STATIC_VIEW = "static-view";
var PURE_VIEW = "pure-view";
var PROPS = "props";
var DATASET = "dataset";
var OBJECT = "object";
var VALUE = "value";
var INPUT = "input";
var CHANGE = "change";
var CUSTOM_WRAPPER = "custom-wrapper";
var TARGET = "target";
var CURRENT_TARGET = "currentTarget";
var TYPE = "type";
var CONFIRM = "confirm";
var TIME_STAMP = "timeStamp";
var KEY_CODE = "keyCode";
var TOUCHMOVE = "touchmove";
var DATE = "Date";
var SET_TIMEOUT = "setTimeout";
var COMPILE_MODE = "compileMode";
var CATCHMOVE = "catchMove";
var CATCH_VIEW = "catch-view";
var COMMENT = "comment";
var ON_LOAD = "onLoad";
var ON_READY = "onReady";
var ON_SHOW = "onShow";
var ON_HIDE = "onHide";
var OPTIONS = "options";
var EXTERNAL_CLASSES = "externalClasses";
var EVENT_CALLBACK_RESULT = "e_result";
var BEHAVIORS = "behaviors";
var A = "a";
var CONTEXT_ACTIONS;
(function(CONTEXT_ACTIONS2) {
    CONTEXT_ACTIONS2["INIT"] = "0";
    CONTEXT_ACTIONS2["RESTORE"] = "1";
    CONTEXT_ACTIONS2["RECOVER"] = "2";
    CONTEXT_ACTIONS2["DESTORY"] = "3";
})(CONTEXT_ACTIONS || (CONTEXT_ACTIONS = {}));
var observers = [];
var MutationObserverImpl = /*#__PURE__*/ function() {
    "use strict";
    function MutationObserverImpl(callback) {
        _class_call_check(this, MutationObserverImpl);
        this.records = [];
        this.callback = callback;
    }
    _create_class(MutationObserverImpl, [
        {
            /**
   * Configures the MutationObserver
   * to begin receiving notifications
   * through its callback function
   * when DOM changes matching the given options occur.
   *
   * Options matching is to be implemented.
   */ key: "observe",
            value: function observe(target, options2) {
                this.disconnect();
                this.target = target;
                this.options = options2 || {};
                observers.push(this);
            }
        },
        {
            /**
   * Stop the MutationObserver instance
   * from receiving further notifications
   * until and unless observe() is called again.
   */ key: "disconnect",
            value: function disconnect() {
                this.target = null;
                var index = observers.indexOf(this);
                if (index >= 0) {
                    observers.splice(index, 1);
                }
            }
        },
        {
            /**
   * Removes all pending notifications
   * from the MutationObserver's notification queue
   * and returns them in a new Array of MutationRecord objects.
   */ key: "takeRecords",
            value: function takeRecords() {
                return this.records.splice(0, this.records.length);
            }
        }
    ]);
    return MutationObserverImpl;
}();
var sidMatches = function(observerTarget, target) {
    return !!observerTarget && observerTarget.sid === (target === null || target === void 0 ? void 0 : target.sid);
};
var isConcerned = function(record, options2) {
    var characterData = options2.characterData, characterDataOldValue = options2.characterDataOldValue, attributes = options2.attributes, attributeOldValue = options2.attributeOldValue, childList = options2.childList;
    switch(record.type){
        case "characterData":
            if (characterData) {
                if (!characterDataOldValue) record.oldValue = null;
                return true;
            }
            return false;
        case "attributes":
            if (attributes) {
                if (!attributeOldValue) record.oldValue = null;
                return true;
            }
            return false;
        case "childList":
            if (childList) {
                return true;
            }
            return false;
    }
};
var pendingMuatations = false;
function logMutation(observer, record) {
    observer.records.push(record);
    if (!pendingMuatations) {
        pendingMuatations = true;
        Promise.resolve().then(function() {
            pendingMuatations = false;
            observers.forEach(function(observer2) {
                return observer2.callback(observer2.takeRecords());
            });
        });
    }
}
function recordMutation(record) {
    observers.forEach(function(observer) {
        var options2 = observer.options;
        for(var t = record.target; t; t = t.parentNode){
            if (sidMatches(observer.target, t) && isConcerned(record, options2)) {
                logMutation(observer, record);
                break;
            }
            if (!options2.subtree) break;
        }
    });
}
var MutationObserver$1 = /*#__PURE__*/ function() {
    "use strict";
    function MutationObserver2(callback) {
        _class_call_check(this, MutationObserver2);
        if (ENABLE_MUTATION_OBSERVER) {
            this.core = new MutationObserverImpl(callback);
        } else {
            if (true) {
                console.warn("[Taro Warning] 若要使用 MutationObserver，请在 Taro 编译配置中设置 'mini.runtime.enableMutationObserver: true'");
            }
            this.core = {
                observe: noop,
                disconnect: noop,
                takeRecords: noop
            };
        }
    }
    _create_class(MutationObserver2, [
        {
            key: "observe",
            value: function observe() {
                for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){
                    args[_key] = arguments[_key];
                }
                var _this_core;
                (_this_core = this.core).observe.apply(_this_core, _to_consumable_array(args));
            }
        },
        {
            key: "disconnect",
            value: function disconnect() {
                this.core.disconnect();
            }
        },
        {
            key: "takeRecords",
            value: function takeRecords() {
                return this.core.takeRecords();
            }
        }
    ], [
        {
            key: "record",
            value: function record(record) {
                recordMutation(record);
            }
        }
    ]);
    return MutationObserver2;
}();
function throttle(fn) {
    var threshold = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 250, scope = arguments.length > 2 ? arguments[2] : void 0;
    var lastTime2 = 0;
    var deferTimer;
    return function() {
        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){
            args[_key] = arguments[_key];
        }
        var context = scope || this;
        var now2 = Date.now();
        if (now2 - lastTime2 > threshold) {
            fn.apply(this, args);
            lastTime2 = now2;
        } else {
            clearTimeout(deferTimer);
            deferTimer = setTimeout(function() {
                lastTime2 = now2;
                fn.apply(context, args);
            }, threshold);
        }
    };
}
var incrementId = function() {
    var chatCodes = [];
    for(var i = 65; i <= 90; i++){
        chatCodes.push(i);
    }
    for(var i1 = 97; i1 <= 122; i1++){
        chatCodes.push(i1);
    }
    var chatCodesLen = chatCodes.length - 1;
    var list = [
        0,
        0
    ];
    return function() {
        var _String;
        var target = list.map(function(item) {
            return chatCodes[item];
        });
        var res = (_String = String).fromCharCode.apply(_String, _to_consumable_array(target));
        var tailIdx = list.length - 1;
        list[tailIdx]++;
        while(list[tailIdx] > chatCodesLen){
            list[tailIdx] = 0;
            tailIdx = tailIdx - 1;
            if (tailIdx < 0) {
                list.push(0);
                break;
            }
            list[tailIdx]++;
        }
        return res;
    };
};
function isElement(node) {
    return node.nodeType === 1;
}
function isText(node) {
    return node.nodeType === 3;
}
function isComment(node) {
    return node.nodeName === COMMENT;
}
function isHasExtractProp(el) {
    var res = Object.keys(el.props).find(function(prop) {
        return !(/^(class|style|id)$/.test(prop) || prop.startsWith("data-"));
    });
    return Boolean(res);
}
function isParentBinded(node, type) {
    var _$_a;
    while(node = (node === null || node === void 0 ? void 0 : node.parentElement) || null){
        if (!node || node.nodeName === ROOT_STR || node.nodeName === "root-portal") {
            return false;
        } else if ((_$_a = node.__handlers[type]) === null || _$_a === void 0 ? void 0 : _$_a.length) {
            return true;
        }
    }
    return false;
}
function shortcutAttr(key) {
    switch(key){
        case STYLE:
            return "st";
        case ID:
            return UID;
        case CLASS:
            return "cl";
        default:
            return key;
    }
}
var customWrapperCache = /* @__PURE__ */ new Map();
function extend(ctor, methodName, options2) {
    if (isFunction(options2)) {
        options2 = {
            value: options2
        };
    }
    Object.defineProperty(ctor.prototype, methodName, Object.assign({
        configurable: true,
        enumerable: true
    }, options2));
}
var componentsAlias$1;
function getComponentsAlias2() {
    if (!componentsAlias$1) {
        componentsAlias$1 = getComponentsAlias(internalComponents);
    }
    return componentsAlias$1;
}
var ClassList = /*#__PURE__*/ function() {
    "use strict";
    function ClassList(className, el) {
        var _this = this;
        _class_call_check(this, ClassList);
        this.tokenList = [];
        this.el = el;
        className.trim().split(/\s+/).forEach(function(token) {
            return _this.tokenList.push(token);
        });
    }
    _create_class(ClassList, [
        {
            key: "value",
            get: function get() {
                return this.toString();
            }
        },
        {
            key: "length",
            get: function get() {
                return this.tokenList.length;
            }
        },
        {
            key: "add",
            value: function add() {
                var index = 0;
                var updated = false;
                var tokens = arguments;
                var length = tokens.length;
                var tokenList = this.tokenList;
                do {
                    var token = tokens[index];
                    if (this.checkTokenIsValid(token) && !~tokenList.indexOf(token)) {
                        tokenList.push(token);
                        updated = true;
                    }
                }while (++index < length);
                if (updated) {
                    this._update();
                }
            }
        },
        {
            key: "remove",
            value: function remove() {
                var i = 0;
                var updated = false;
                var tokens = arguments;
                var length = tokens.length;
                var tokenList = this.tokenList;
                do {
                    var token = tokens[i] + "";
                    if (!this.checkTokenIsValid(token)) continue;
                    var index = tokenList.indexOf(token);
                    if (~tokenList.indexOf(token)) {
                        tokenList.splice(index, 1);
                        updated = true;
                    }
                }while (++i < length);
                if (updated) {
                    this._update();
                }
            }
        },
        {
            key: "contains",
            value: function contains1(token) {
                if (!this.checkTokenIsValid(token)) return false;
                return !!~this.tokenList.indexOf(token);
            }
        },
        {
            key: "toggle",
            value: function toggle(token, force) {
                var result = this.contains(token);
                var method = result ? force !== true && "remove" : force !== false && "add";
                if (method) {
                    this[method](token);
                }
                if (force === true || force === false) {
                    return force;
                } else {
                    return !result;
                }
            }
        },
        {
            key: "replace",
            value: function replace(token, replacement_token) {
                if (!this.checkTokenIsValid(token) || !this.checkTokenIsValid(replacement_token)) return;
                var index = this.tokenList.indexOf(token);
                if (~index) {
                    this.tokenList.splice(index, 1, replacement_token);
                    this._update();
                }
            }
        },
        {
            key: "toString",
            value: function toString() {
                return this.tokenList.filter(function(v) {
                    return v !== "";
                }).join(" ");
            }
        },
        {
            key: "checkTokenIsValid",
            value: function checkTokenIsValid(token) {
                if (token === "" || /\s/.test(token)) return false;
                return true;
            }
        },
        {
            key: "_update",
            value: function _update() {
                this.el.className = this.value;
            }
        }
    ]);
    return ClassList;
}();
var EventSource = /*#__PURE__*/ function(Map1) {
    "use strict";
    _inherits(EventSource, Map1);
    var _super = _create_super(EventSource);
    function EventSource() {
        _class_call_check(this, EventSource);
        return _super.apply(this, arguments);
    }
    _create_class(EventSource, [
        {
            key: "removeNode",
            value: function removeNode(child) {
                var sid = child.sid, uid = child.uid;
                this.delete(sid);
                if (uid !== sid && uid) this.delete(uid);
            }
        },
        {
            key: "removeNodeTree",
            value: function removeNodeTree(child) {
                var _this = this;
                this.removeNode(child);
                var childNodes = child.childNodes;
                childNodes.forEach(function(node) {
                    return _this.removeNodeTree(node);
                });
            }
        }
    ]);
    return EventSource;
}(_wrap_native_super(Map));
var eventSource = new EventSource();
var env = {
    window: true ? window : EMPTY_OBJ,
    document: true ? document : EMPTY_OBJ
};
var SPECIAL_NODES;
var componentsAlias;
function hydrate(node) {
    var _$_a;
    componentsAlias || (componentsAlias = getComponentsAlias2());
    SPECIAL_NODES || (SPECIAL_NODES = hooks.call("getSpecialNodes"));
    var nodeName = node.nodeName;
    var compileModeName = null;
    if (isText(node)) {
        var _obj;
        return _obj = {
            sid: node.sid
        }, _define_property(_obj, "v", node.nodeValue), _define_property(_obj, "nn", ((_$_a = componentsAlias[nodeName]) === null || _$_a === void 0 ? void 0 : _$_a._num) || "8"), _obj;
    }
    var _obj1;
    var data = (_obj1 = {}, _define_property(_obj1, "nn", nodeName), _define_property(_obj1, "sid", node.sid), _obj1);
    if (node.uid !== node.sid) {
        data.uid = node.uid;
    }
    if (!node.isAnyEventBinded() && SPECIAL_NODES.indexOf(nodeName) > -1) {
        data["nn"] = "static-".concat(nodeName);
        if (nodeName === VIEW && !isHasExtractProp(node)) {
            data["nn"] = PURE_VIEW;
        }
    }
    var props = node.props;
    for(var prop in props){
        var propInCamelCase = toCamelCase(prop);
        if (!prop.startsWith("data-") && // 在 node.dataset 的数据
        prop !== CLASS && prop !== STYLE && prop !== ID && propInCamelCase !== CATCHMOVE && propInCamelCase !== COMPILE_MODE) {
            data[propInCamelCase] = props[prop];
        }
        if (nodeName === VIEW && propInCamelCase === CATCHMOVE && props[prop] !== false) {
            data["nn"] = CATCH_VIEW;
        }
        if (propInCamelCase === COMPILE_MODE) {
            compileModeName = props[prop];
        }
    }
    data["cn"] = node.childNodes.filter(function(node2) {
        return !isComment(node2);
    }).map(hydrate);
    if (node.className !== "") {
        data["cl"] = node.className;
    }
    var cssText = node.cssText;
    if (cssText !== "" && nodeName !== "swiper-item") {
        data["st"] = cssText;
    }
    hooks.call("modifyHydrateData", data, node);
    var nn = data["nn"];
    var componentAlias = componentsAlias[nn];
    if (componentAlias) {
        data["nn"] = componentAlias._num;
        for(var prop1 in data){
            if (prop1 in componentAlias) {
                data[componentAlias[prop1]] = data[prop1];
                delete data[prop1];
            }
        }
    }
    if (compileModeName !== null) {
        data["nn"] = compileModeName;
    }
    var resData = hooks.call("transferHydrateData", data, node, componentAlias);
    return resData || data;
}
var TaroEventTarget = /*#__PURE__*/ function() {
    "use strict";
    function TaroEventTarget() {
        _class_call_check(this, TaroEventTarget);
        this.__handlers = {};
    }
    _create_class(TaroEventTarget, [
        {
            key: "addEventListener",
            value: function addEventListener(type, handler, options2) {
                type = type.toLowerCase();
                hooks.call("onAddEvent", type, handler, options2, this);
                if (type === "regionchange") {
                    this.addEventListener("begin", handler, options2);
                    this.addEventListener("end", handler, options2);
                    return;
                }
                var isCapture = Boolean(options2);
                var isOnce = false;
                if (isObject(options2)) {
                    isCapture = Boolean(options2.capture);
                    isOnce = Boolean(options2.once);
                }
                if (isOnce) {
                    var wrapper = function wrapper1() {
                        handler.apply(this, arguments);
                        this.removeEventListener(type, wrapper);
                    };
                    this.addEventListener(type, wrapper, Object.assign(Object.assign({}, options2), {
                        once: false
                    }));
                    return;
                }
                warn(isCapture, "Taro 暂未实现 event 的 capture 特性。");
                var oldHandler = handler;
                handler = function handler() {
                    return oldHandler.apply(this, arguments);
                };
                handler.oldHandler = oldHandler;
                var handlers = this.__handlers[type];
                if (isArray(handlers)) {
                    handlers.push(handler);
                } else {
                    this.__handlers[type] = [
                        handler
                    ];
                }
            }
        },
        {
            key: "removeEventListener",
            value: function removeEventListener(type, handler) {
                type = type.toLowerCase();
                if (type === "regionchange") {
                    this.removeEventListener("begin", handler);
                    this.removeEventListener("end", handler);
                    return;
                }
                if (!handler) {
                    return;
                }
                var handlers = this.__handlers[type];
                if (!isArray(handlers)) {
                    return;
                }
                var index = handlers.findIndex(function(item) {
                    if (item === handler || item.oldHandler === handler) return true;
                });
                warn(index === -1, "事件: '".concat(type, "' 没有注册在 DOM 中，因此不会被移除。"));
                handlers.splice(index, 1);
            }
        },
        {
            key: "isAnyEventBinded",
            value: function isAnyEventBinded() {
                var handlers = this.__handlers;
                var isAnyEventBinded = Object.keys(handlers).find(function(key) {
                    return handlers[key].length;
                });
                return Boolean(isAnyEventBinded);
            }
        }
    ]);
    return TaroEventTarget;
}();
var CHILDNODES = "cn";
var nodeId = incrementId();
var TaroNode = /*#__PURE__*/ function(TaroEventTarget) {
    "use strict";
    _inherits(_TaroNode, TaroEventTarget);
    var _super = _create_super(_TaroNode);
    function _TaroNode() {
        _class_call_check(this, _TaroNode);
        var _this;
        _this = _super.call(this);
        _this.parentNode = null;
        _this.childNodes = [];
        _this.hydrate = function(node) {
            return function() {
                return hydrate(node);
            };
        };
        _this.uid = "_" + nodeId();
        _this.sid = _this.uid;
        eventSource.set(_this.sid, _assert_this_initialized(_this));
        return _this;
    }
    _create_class(_TaroNode, [
        {
            key: "updateChildNodes",
            value: function updateChildNodes(isClean) {
                var _this = this;
                var cleanChildNodes = function() {
                    return [];
                };
                var rerenderChildNodes = function() {
                    var childNodes = _this.childNodes.filter(function(node) {
                        return !isComment(node);
                    });
                    return childNodes.map(hydrate);
                };
                this.enqueueUpdate({
                    path: "".concat(this._path, ".").concat(CHILDNODES),
                    value: isClean ? cleanChildNodes : rerenderChildNodes
                });
            }
        },
        {
            key: "updateSingleChild",
            value: function updateSingleChild(index) {
                var _this = this;
                this.childNodes.forEach(function(child, childIndex) {
                    if (isComment(child)) return;
                    if (index && childIndex < index) return;
                    _this.enqueueUpdate({
                        path: child._path,
                        value: _this.hydrate(child)
                    });
                });
            }
        },
        {
            key: "_root",
            get: function get() {
                var _$_a;
                return ((_$_a = this.parentNode) === null || _$_a === void 0 ? void 0 : _$_a._root) || null;
            }
        },
        {
            key: "findIndex",
            value: function findIndex(refChild) {
                var index = this.childNodes.indexOf(refChild);
                ensure(index !== -1, "The node to be replaced is not a child of this node.");
                return index;
            }
        },
        {
            key: "_path",
            get: function get() {
                var parentNode = this.parentNode;
                if (parentNode) {
                    var list = parentNode.childNodes.filter(function(node) {
                        return !isComment(node);
                    });
                    var indexOfNode = list.indexOf(this);
                    var index = hooks.call("getPathIndex", indexOfNode);
                    return "".concat(parentNode._path, ".").concat(CHILDNODES, ".").concat(index);
                }
                return "";
            }
        },
        {
            key: "nextSibling",
            get: function get() {
                var parentNode = this.parentNode;
                return (parentNode === null || parentNode === void 0 ? void 0 : parentNode.childNodes[parentNode.findIndex(this) + 1]) || null;
            }
        },
        {
            key: "previousSibling",
            get: function get() {
                var parentNode = this.parentNode;
                return (parentNode === null || parentNode === void 0 ? void 0 : parentNode.childNodes[parentNode.findIndex(this) - 1]) || null;
            }
        },
        {
            key: "parentElement",
            get: function get() {
                var parentNode = this.parentNode;
                if ((parentNode === null || parentNode === void 0 ? void 0 : parentNode.nodeType) === 1) {
                    return parentNode;
                }
                return null;
            }
        },
        {
            key: "firstChild",
            get: function get() {
                return this.childNodes[0] || null;
            }
        },
        {
            key: "lastChild",
            get: function get() {
                var childNodes = this.childNodes;
                return childNodes[childNodes.length - 1] || null;
            }
        },
        {
            key: "textContent",
            set: /**
   * @textContent 目前只能置空子元素
   * @TODO 等待完整 innerHTML 实现
   */ // eslint-disable-next-line accessor-pairs
            function set(text) {
                var removedNodes = this.childNodes.slice();
                var addedNodes = [];
                while(this.firstChild){
                    this.removeChild(this.firstChild, {
                        doUpdate: false
                    });
                }
                if (text === "") {
                    this.updateChildNodes(true);
                } else {
                    var newText = env.document.createTextNode(text);
                    addedNodes.push(newText);
                    this.appendChild(newText);
                    this.updateChildNodes();
                }
                MutationObserver$1.record({
                    type: "childList",
                    target: this,
                    removedNodes: removedNodes,
                    addedNodes: addedNodes
                });
            }
        },
        {
            /**
   * @doc https://developer.mozilla.org/zh-CN/docs/Web/API/Node/insertBefore
   * @scenario
   * [A,B,C]
   *   1. insert D before C, D has no parent
   *   2. insert D before C, D has the same parent of C
   *   3. insert D before C, D has the different parent of C
   */ key: "insertBefore",
            value: function insertBefore(newChild, refChild, isReplace) {
                var _this = this;
                if (newChild.nodeName === DOCUMENT_FRAGMENT) {
                    newChild.childNodes.reduceRight(function(previousValue, currentValue) {
                        _this.insertBefore(currentValue, previousValue);
                        return currentValue;
                    }, refChild);
                    return newChild;
                }
                newChild.remove({
                    cleanRef: false
                });
                var index = 0;
                newChild.parentNode = this;
                if (refChild) {
                    index = this.findIndex(refChild);
                    this.childNodes.splice(index, 0, newChild);
                } else {
                    this.childNodes.push(newChild);
                }
                var childNodesLength = this.childNodes.length;
                if (this._root) {
                    if (!refChild) {
                        var isOnlyChild = childNodesLength === 1;
                        if (isOnlyChild) {
                            this.updateChildNodes();
                        } else {
                            this.enqueueUpdate({
                                path: newChild._path,
                                value: this.hydrate(newChild)
                            });
                        }
                    } else if (isReplace) {
                        this.enqueueUpdate({
                            path: newChild._path,
                            value: this.hydrate(newChild)
                        });
                    } else {
                        var mark = childNodesLength * 2 / 3;
                        if (mark > index) {
                            this.updateChildNodes();
                        } else {
                            this.updateSingleChild(index);
                        }
                    }
                }
                MutationObserver$1.record({
                    type: "childList",
                    target: this,
                    addedNodes: [
                        newChild
                    ],
                    removedNodes: isReplace ? [
                        refChild
                    ] : [],
                    nextSibling: isReplace ? refChild.nextSibling : refChild || null,
                    previousSibling: newChild.previousSibling
                });
                return newChild;
            }
        },
        {
            /**
   * @doc https://developer.mozilla.org/zh-CN/docs/Web/API/Node/appendChild
   * @scenario
   * [A,B,C]
   *   1. append C, C has no parent
   *   2. append C, C has the same parent of B
   *   3. append C, C has the different parent of B
   */ key: "appendChild",
            value: function appendChild(newChild) {
                return this.insertBefore(newChild);
            }
        },
        {
            /**
   * @doc https://developer.mozilla.org/zh-CN/docs/Web/API/Node/replaceChild
   * @scenario
   * [A,B,C]
   *   1. replace B with C, C has no parent
   *   2. replace B with C, C has no parent, C has the same parent of B
   *   3. replace B with C, C has no parent, C has the different parent of B
   */ key: "replaceChild",
            value: function replaceChild(newChild, oldChild) {
                if (oldChild.parentNode !== this) return;
                this.insertBefore(newChild, oldChild, true);
                oldChild.remove({
                    doUpdate: false
                });
                return oldChild;
            }
        },
        {
            /**
   * @doc https://developer.mozilla.org/zh-CN/docs/Web/API/Node/removeChild
   * @scenario
   * [A,B,C]
   *   1. remove A or B
   *   2. remove C
   */ key: "removeChild",
            value: function removeChild(child) {
                var options2 = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
                var cleanRef = options2.cleanRef, doUpdate = options2.doUpdate;
                if (cleanRef !== false && doUpdate !== false) {
                    MutationObserver$1.record({
                        type: "childList",
                        target: this,
                        removedNodes: [
                            child
                        ],
                        nextSibling: child.nextSibling,
                        previousSibling: child.previousSibling
                    });
                }
                var index = this.findIndex(child);
                this.childNodes.splice(index, 1);
                child.parentNode = null;
                if (cleanRef !== false) {
                    eventSource.removeNodeTree(child);
                }
                if (this._root && doUpdate !== false) {
                    this.updateChildNodes();
                }
                return child;
            }
        },
        {
            key: "remove",
            value: function remove(options2) {
                var _$_a;
                (_$_a = this.parentNode) === null || _$_a === void 0 ? void 0 : _$_a.removeChild(this, options2);
            }
        },
        {
            key: "hasChildNodes",
            value: function hasChildNodes() {
                return this.childNodes.length > 0;
            }
        },
        {
            key: "enqueueUpdate",
            value: function enqueueUpdate(payload) {
                var _$_a;
                (_$_a = this._root) === null || _$_a === void 0 ? void 0 : _$_a.enqueueUpdate(payload);
            }
        },
        {
            key: "ownerDocument",
            get: function get() {
                return env.document;
            }
        }
    ], [
        {
            key: "extend",
            value: function extend1(methodName, options2) {
                extend(_TaroNode, methodName, options2);
            }
        }
    ]);
    return _TaroNode;
}(TaroEventTarget);
var WEBKIT = "webkit";
var styleProperties = [
    "all",
    "appearance",
    "blockOverflow",
    "blockSize",
    "bottom",
    "clear",
    "contain",
    "content",
    "continue",
    "cursor",
    "direction",
    "display",
    "filter",
    "float",
    "gap",
    "height",
    "inset",
    "isolation",
    "left",
    "letterSpacing",
    "lightingColor",
    "markerSide",
    "mixBlendMode",
    "opacity",
    "order",
    "position",
    "quotes",
    "resize",
    "right",
    "rowGap",
    "tabSize",
    "tableLayout",
    "top",
    "userSelect",
    "verticalAlign",
    "visibility",
    "voiceFamily",
    "volume",
    "whiteSpace",
    "widows",
    "width",
    "zIndex",
    "pointerEvents",
    "aspectRatio"
];
function combine(prefix, list, excludeSelf) {
    !excludeSelf && styleProperties.push(prefix);
    list.forEach(function(item) {
        styleProperties.push(prefix + item);
        if (prefix === WEBKIT) {
            styleProperties.push("Webkit" + item);
        }
    });
}
var color = "Color";
var style = "Style";
var width = "Width";
var image = "Image";
var size = "Size";
var color_style_width = [
    color,
    style,
    width
];
var fitlength_fitwidth_image = [
    "FitLength",
    "FitWidth",
    image
];
var fitlength_fitwidth_image_radius = _to_consumable_array(fitlength_fitwidth_image).concat([
    "Radius"
]);
var color_style_width_fitlength_fitwidth_image = _to_consumable_array(color_style_width).concat(_to_consumable_array(fitlength_fitwidth_image));
var endRadius_startRadius = [
    "EndRadius",
    "StartRadius"
];
var bottom_left_right_top = [
    "Bottom",
    "Left",
    "Right",
    "Top"
];
var end_start = [
    "End",
    "Start"
];
var content_items_self = [
    "Content",
    "Items",
    "Self"
];
var blockSize_height_inlineSize_width = [
    "BlockSize",
    "Height",
    "InlineSize",
    width
];
var after_before = [
    "After",
    "Before"
];
combine("borderBlock", color_style_width);
combine("borderBlockEnd", color_style_width);
combine("borderBlockStart", color_style_width);
combine("outline", _to_consumable_array(color_style_width).concat([
    "Offset"
]));
combine("border", _to_consumable_array(color_style_width).concat([
    "Boundary",
    "Break",
    "Collapse",
    "Radius",
    "Spacing"
]));
combine("borderFit", [
    "Length",
    width
]);
combine("borderInline", color_style_width);
combine("borderInlineEnd", color_style_width);
combine("borderInlineStart", color_style_width);
combine("borderLeft", color_style_width_fitlength_fitwidth_image);
combine("borderRight", color_style_width_fitlength_fitwidth_image);
combine("borderTop", color_style_width_fitlength_fitwidth_image);
combine("borderBottom", color_style_width_fitlength_fitwidth_image);
combine("textDecoration", [
    color,
    style,
    "Line"
]);
combine("textEmphasis", [
    color,
    style,
    "Position"
]);
combine("scrollMargin", bottom_left_right_top);
combine("scrollPadding", bottom_left_right_top);
combine("padding", bottom_left_right_top);
combine("margin", _to_consumable_array(bottom_left_right_top).concat([
    "Trim"
]));
combine("scrollMarginBlock", end_start);
combine("scrollMarginInline", end_start);
combine("scrollPaddingBlock", end_start);
combine("scrollPaddingInline", end_start);
combine("gridColumn", end_start);
combine("gridRow", end_start);
combine("insetBlock", end_start);
combine("insetInline", end_start);
combine("marginBlock", end_start);
combine("marginInline", end_start);
combine("paddingBlock", end_start);
combine("paddingInline", end_start);
combine("pause", after_before);
combine("cue", after_before);
combine("mask", [
    "Clip",
    "Composite",
    image,
    "Mode",
    "Origin",
    "Position",
    "Repeat",
    size,
    "Type"
]);
combine("borderImage", [
    "Outset",
    "Repeat",
    "Slice",
    "Source",
    "Transform",
    width
]);
combine("maskBorder", [
    "Mode",
    "Outset",
    "Repeat",
    "Slice",
    "Source",
    width
]);
combine("font", [
    "Family",
    "FeatureSettings",
    "Kerning",
    "LanguageOverride",
    "MaxSize",
    "MinSize",
    "OpticalSizing",
    "Palette",
    size,
    "SizeAdjust",
    "Stretch",
    style,
    "Weight",
    "VariationSettings"
]);
combine("transform", [
    "Box",
    "Origin",
    style
]);
combine("background", [
    color,
    image,
    "Attachment",
    "BlendMode",
    "Clip",
    "Origin",
    "Position",
    "Repeat",
    size
]);
combine("listStyle", [
    image,
    "Position",
    "Type"
]);
combine("scrollSnap", [
    "Align",
    "Stop",
    "Type"
]);
combine("grid", [
    "Area",
    "AutoColumns",
    "AutoFlow",
    "AutoRows"
]);
combine("gridTemplate", [
    "Areas",
    "Columns",
    "Rows"
]);
combine("overflow", [
    "Block",
    "Inline",
    "Wrap",
    "X",
    "Y"
]);
combine("transition", [
    "Delay",
    "Duration",
    "Property",
    "TimingFunction"
]);
combine("color", [
    "Adjust",
    "InterpolationFilters",
    "Scheme"
]);
combine("textAlign", [
    "All",
    "Last"
]);
combine("page", [
    "BreakAfter",
    "BreakBefore",
    "BreakInside"
]);
combine("animation", [
    "Delay",
    "Direction",
    "Duration",
    "FillMode",
    "IterationCount",
    "Name",
    "PlayState",
    "TimingFunction"
]);
combine("flex", [
    "Basis",
    "Direction",
    "Flow",
    "Grow",
    "Shrink",
    "Wrap"
]);
combine("offset", _to_consumable_array(after_before).concat(_to_consumable_array(end_start), [
    "Anchor",
    "Distance",
    "Path",
    "Position",
    "Rotate"
]));
combine("perspective", [
    "Origin"
]);
combine("clip", [
    "Path",
    "Rule"
]);
combine("flow", [
    "From",
    "Into"
]);
combine("align", [
    "Content",
    "Items",
    "Self"
], true);
combine("alignment", [
    "Adjust",
    "Baseline"
], true);
combine("borderStart", endRadius_startRadius, true);
combine("borderEnd", endRadius_startRadius, true);
combine("borderCorner", [
    "Fit",
    image,
    "ImageTransform"
], true);
combine("borderTopLeft", fitlength_fitwidth_image_radius, true);
combine("borderTopRight", fitlength_fitwidth_image_radius, true);
combine("borderBottomLeft", fitlength_fitwidth_image_radius, true);
combine("borderBottomRight", fitlength_fitwidth_image_radius, true);
combine("column", [
    "s",
    "Count",
    "Fill",
    "Gap",
    "Rule",
    "RuleColor",
    "RuleStyle",
    "RuleWidth",
    "Span",
    width
], true);
combine("break", _to_consumable_array(after_before).concat([
    "Inside"
]), true);
combine("wrap", _to_consumable_array(after_before).concat([
    "Flow",
    "Inside",
    "Through"
]), true);
combine("justify", content_items_self, true);
combine("place", content_items_self, true);
combine("max", _to_consumable_array(blockSize_height_inlineSize_width).concat([
    "Lines"
]), true);
combine("min", blockSize_height_inlineSize_width, true);
combine("line", [
    "Break",
    "Clamp",
    "Grid",
    "Height",
    "Padding",
    "Snap"
], true);
combine("inline", [
    "BoxAlign",
    size,
    "Sizing"
], true);
combine("text", [
    "CombineUpright",
    "GroupAlign",
    "Height",
    "Indent",
    "Justify",
    "Orientation",
    "Overflow",
    "Shadow",
    "SpaceCollapse",
    "SpaceTrim",
    "Spacing",
    "Transform",
    "UnderlinePosition",
    "Wrap"
], true);
combine("shape", [
    "ImageThreshold",
    "Inside",
    "Margin",
    "Outside"
], true);
combine("word", [
    "Break",
    "Spacing",
    "Wrap"
], true);
combine("object", [
    "Fit",
    "Position"
], true);
combine("box", [
    "DecorationBreak",
    "Shadow",
    "Sizing",
    "Snap"
], true);
combine(WEBKIT, [
    "LineClamp",
    "BoxOrient",
    "TextFillColor",
    "TextStroke",
    "TextStrokeColor",
    "TextStrokeWidth"
], true);
function recordCss(obj) {
    MutationObserver$1.record({
        type: "attributes",
        target: obj._element,
        attributeName: "style",
        oldValue: obj.cssText
    });
}
function enqueueUpdate(obj) {
    var element = obj._element;
    if (element._root) {
        element.enqueueUpdate({
            path: "".concat(element._path, ".", "st"),
            value: obj.cssText
        });
    }
}
function setStyle(newVal, styleKey) {
    warn(isString(newVal) && newVal.length > PROPERTY_THRESHOLD, "Style 属性 ".concat(styleKey, " 的值数据量过大，可能会影响渲染性能，考虑使用 CSS 类或其它方案替代。"));
    var old = this[styleKey];
    if (old === newVal) return;
    !this._pending && recordCss(this);
    if (isNull(newVal) || isUndefined(newVal) || newVal === "") {
        this._usedStyleProp.delete(styleKey);
        delete this._value[styleKey];
    } else {
        this._usedStyleProp.add(styleKey);
        this._value[styleKey] = newVal;
    }
    !this._pending && enqueueUpdate(this);
}
function initStyle(ctor, styleProperties2) {
    var _loop = function(i) {
        var styleKey = styleProperties2[i];
        if (ctor[styleKey]) return {
            v: void void 0
        };
        properties[styleKey] = {
            get: function get() {
                var val = this._value[styleKey];
                return isNull(val) || isUndefined(val) ? "" : val;
            },
            set: function set(newVal) {
                setStyle.call(this, newVal, styleKey);
            }
        };
    };
    var properties = {};
    for(var i = 0; i < styleProperties2.length; i++){
        var _ret = _loop(i);
        if (_type_of(_ret) === "object") return _ret.v;
    }
    Object.defineProperties(ctor.prototype, properties);
}
function isCssVariable(propertyName) {
    return /^--/.test(propertyName);
}
var Style = /*#__PURE__*/ function() {
    "use strict";
    function Style(element) {
        _class_call_check(this, Style);
        this._element = element;
        this._usedStyleProp = /* @__PURE__ */ new Set();
        this._value = {};
    }
    _create_class(Style, [
        {
            key: "setCssVariables",
            value: function setCssVariables(styleKey) {
                var _this = this;
                this.hasOwnProperty(styleKey) || Object.defineProperty(this, styleKey, {
                    enumerable: true,
                    configurable: true,
                    get: function() {
                        return _this._value[styleKey] || "";
                    },
                    set: function(newVal) {
                        setStyle.call(_this, newVal, styleKey);
                    }
                });
            }
        },
        {
            key: "cssText",
            get: function get() {
                var _this = this;
                if (!this._usedStyleProp.size) return "";
                var texts = [];
                this._usedStyleProp.forEach(function(key) {
                    var val = _this[key];
                    if (isNull(val) || isUndefined(val)) return;
                    var styleName = isCssVariable(key) ? key : toDashed(key);
                    if (styleName.indexOf("webkit") === 0 || styleName.indexOf("Webkit") === 0) {
                        styleName = "-".concat(styleName);
                    }
                    texts.push("".concat(styleName, ": ").concat(val, ";"));
                });
                return texts.join(" ");
            },
            set: function set(str) {
                var _this = this;
                this._pending = true;
                recordCss(this);
                this._usedStyleProp.forEach(function(prop) {
                    _this.removeProperty(prop);
                });
                if (str === "" || isUndefined(str) || isNull(str)) {
                    this._pending = false;
                    enqueueUpdate(this);
                    return;
                }
                var rules = str.split(";");
                for(var i = 0; i < rules.length; i++){
                    var rule = rules[i].trim();
                    if (rule === "") {
                        continue;
                    }
                    var _rule_split = _to_array(rule.split(":")), propName = _rule_split[0], valList = _rule_split.slice(1);
                    var val = valList.join(":");
                    if (isUndefined(val)) {
                        continue;
                    }
                    this.setProperty(propName.trim(), val.trim());
                }
                this._pending = false;
                enqueueUpdate(this);
            }
        },
        {
            key: "setProperty",
            value: function setProperty(propertyName, value) {
                if (propertyName[0] === "-") {
                    this.setCssVariables(propertyName);
                } else {
                    propertyName = toCamelCase(propertyName);
                }
                if (isNull(value) || isUndefined(value)) {
                    this.removeProperty(propertyName);
                } else {
                    this[propertyName] = value;
                }
            }
        },
        {
            key: "removeProperty",
            value: function removeProperty(propertyName) {
                propertyName = toCamelCase(propertyName);
                if (!this._usedStyleProp.has(propertyName)) {
                    return "";
                }
                var value = this[propertyName];
                this[propertyName] = void 0;
                return value;
            }
        },
        {
            key: "getPropertyValue",
            value: function getPropertyValue(propertyName) {
                propertyName = toCamelCase(propertyName);
                var value = this[propertyName];
                if (!value) {
                    return "";
                }
                return value;
            }
        }
    ]);
    return Style;
}();
initStyle(Style, styleProperties);
hooks.tap("injectNewStyleProperties", function(newStyleProperties) {
    if (isArray(newStyleProperties)) {
        initStyle(Style, newStyleProperties);
    } else {
        if (typeof newStyleProperties !== "string") return;
        initStyle(Style, [
            newStyleProperties
        ]);
    }
});
function returnTrue() {
    return true;
}
function treeToArray(root, predict) {
    var array = [];
    var filter = predict !== null && predict !== void 0 ? predict : returnTrue;
    var object = root;
    while(object){
        if (object.nodeType === 1 && filter(object)) {
            array.push(object);
        }
        object = following(object, root);
    }
    return array;
}
function following(el, root) {
    var firstChild = el.firstChild;
    var isElmentTypeValid = el.nodeType === 1 || el.nodeType === 9;
    if (firstChild && isElmentTypeValid) {
        return firstChild;
    }
    var current = el;
    do {
        if (current === root) {
            return null;
        }
        var nextSibling = current.nextSibling;
        if (nextSibling) {
            return nextSibling;
        }
        current = current.parentElement;
    }while (current);
    return null;
}
var TaroElement = /*#__PURE__*/ function(TaroNode) {
    "use strict";
    _inherits(_TaroElement, TaroNode);
    var _super = _create_super(_TaroElement);
    function _TaroElement() {
        _class_call_check(this, _TaroElement);
        var _this;
        _this = _super.call(this);
        _this.props = {};
        _this.dataset = EMPTY_OBJ;
        _this.nodeType = 1;
        _this.style = new Style(_assert_this_initialized(_this));
        hooks.call("patchElement", _assert_this_initialized(_this));
        return _this;
    }
    _create_class(_TaroElement, [
        {
            key: "_stopPropagation",
            value: function _stopPropagation(event) {
                var target = this;
                while(target = target.parentNode){
                    var listeners = target.__handlers[event.type];
                    if (!isArray(listeners)) {
                        continue;
                    }
                    for(var i = listeners.length; i--;){
                        var l = listeners[i];
                        l._stop = true;
                    }
                }
            }
        },
        {
            key: "id",
            get: function get() {
                return this.getAttribute(ID);
            },
            set: function set(val) {
                this.setAttribute(ID, val);
            }
        },
        {
            key: "className",
            get: function get() {
                return this.getAttribute(CLASS) || "";
            },
            set: function set(val) {
                this.setAttribute(CLASS, val);
            }
        },
        {
            key: "cssText",
            get: function get() {
                return this.getAttribute(STYLE) || "";
            }
        },
        {
            key: "classList",
            get: function get() {
                return new ClassList(this.className, this);
            }
        },
        {
            key: "children",
            get: function get() {
                return this.childNodes.filter(isElement);
            }
        },
        {
            key: "attributes",
            get: function get() {
                var props = this.props;
                var propKeys = Object.keys(props);
                var style2 = this.style.cssText;
                var attrs = propKeys.map(function(key) {
                    return {
                        name: key,
                        value: props[key]
                    };
                });
                return attrs.concat(style2 ? {
                    name: STYLE,
                    value: style2
                } : []);
            }
        },
        {
            key: "textContent",
            get: function get() {
                var text = "";
                var childNodes = this.childNodes;
                for(var i = 0; i < childNodes.length; i++){
                    text += childNodes[i].textContent;
                }
                return text;
            },
            set: function set(text) {
                _set(_get_prototype_of(_TaroElement.prototype), "textContent", text, this, true);
            }
        },
        {
            key: "hasAttribute",
            value: function hasAttribute(qualifiedName) {
                return !isUndefined(this.props[qualifiedName]);
            }
        },
        {
            key: "hasAttributes",
            value: function hasAttributes() {
                return this.attributes.length > 0;
            }
        },
        {
            key: "focus",
            get: function get() {
                return function() {
                    this.setAttribute(FOCUS, true);
                };
            },
            set: // 兼容 Vue3，详情请见：https://github.com/NervJS/taro/issues/10579
            function set(value) {
                this.setAttribute(FOCUS, value);
            }
        },
        {
            key: "blur",
            value: function blur() {
                this.setAttribute(FOCUS, false);
            }
        },
        {
            key: "setAttribute",
            value: function setAttribute(qualifiedName, value) {
                warn(isString(value) && value.length > PROPERTY_THRESHOLD, "元素 ".concat(this.nodeName, " 的 ").concat(qualifiedName, " 属性值数据量过大，可能会影响渲染性能。考虑降低图片转为 base64 的阈值或在 CSS 中使用 base64。"));
                var isPureView = this.nodeName === VIEW && !isHasExtractProp(this) && !this.isAnyEventBinded();
                if (qualifiedName !== STYLE) {
                    MutationObserver$1.record({
                        target: this,
                        type: "attributes",
                        attributeName: qualifiedName,
                        oldValue: this.getAttribute(qualifiedName)
                    });
                }
                switch(qualifiedName){
                    case STYLE:
                        this.style.cssText = value;
                        break;
                    case ID:
                        if (this.uid !== this.sid) {
                            eventSource.delete(this.uid);
                        }
                        value = String(value);
                        this.props[qualifiedName] = this.uid = value;
                        eventSource.set(value, this);
                        break;
                    default:
                        this.props[qualifiedName] = value;
                        if (qualifiedName.startsWith("data-")) {
                            if (this.dataset === EMPTY_OBJ) {
                                this.dataset = /* @__PURE__ */ Object.create(null);
                            }
                            this.dataset[toCamelCase(qualifiedName.replace(/^data-/, ""))] = value;
                        }
                        break;
                }
                if (!this._root) return;
                var componentsAlias2 = getComponentsAlias2();
                var _alias = componentsAlias2[this.nodeName];
                var viewAlias = componentsAlias2[VIEW]._num;
                var staticViewAlias = componentsAlias2[STATIC_VIEW]._num;
                var catchViewAlias = componentsAlias2[CATCH_VIEW]._num;
                var _path = this._path;
                qualifiedName = shortcutAttr(qualifiedName);
                var qualifiedNameInCamelCase = toCamelCase(qualifiedName);
                var payload = {
                    path: "".concat(_path, ".").concat(qualifiedNameInCamelCase),
                    value: isFunction(value) ? function() {
                        return value;
                    } : value
                };
                hooks.call("modifySetAttrPayload", this, qualifiedName, payload, componentsAlias2);
                if (_alias) {
                    var qualifiedNameAlias = _alias[qualifiedNameInCamelCase] || qualifiedName;
                    payload.path = "".concat(_path, ".").concat(toCamelCase(qualifiedNameAlias));
                }
                this.enqueueUpdate(payload);
                if (this.nodeName === VIEW) {
                    if (qualifiedNameInCamelCase === CATCHMOVE) {
                        this.enqueueUpdate({
                            path: "".concat(_path, ".", "nn"),
                            value: value ? catchViewAlias : this.isAnyEventBinded() ? viewAlias : staticViewAlias
                        });
                    } else if (isPureView && isHasExtractProp(this)) {
                        this.enqueueUpdate({
                            path: "".concat(_path, ".", "nn"),
                            value: staticViewAlias
                        });
                    }
                }
            }
        },
        {
            key: "removeAttribute",
            value: function removeAttribute(qualifiedName) {
                var isStaticView = this.nodeName === VIEW && isHasExtractProp(this) && !this.isAnyEventBinded();
                MutationObserver$1.record({
                    target: this,
                    type: "attributes",
                    attributeName: qualifiedName,
                    oldValue: this.getAttribute(qualifiedName)
                });
                if (qualifiedName === STYLE) {
                    this.style.cssText = "";
                } else {
                    var isInterrupt = hooks.call("onRemoveAttribute", this, qualifiedName);
                    if (isInterrupt) {
                        return;
                    }
                    if (!this.props.hasOwnProperty(qualifiedName)) {
                        return;
                    }
                    delete this.props[qualifiedName];
                }
                if (!this._root) return;
                var componentsAlias2 = getComponentsAlias2();
                var _alias = componentsAlias2[this.nodeName];
                var viewAlias = componentsAlias2[VIEW]._num;
                var staticViewAlias = componentsAlias2[STATIC_VIEW]._num;
                var pureViewAlias = componentsAlias2[PURE_VIEW]._num;
                var _path = this._path;
                qualifiedName = shortcutAttr(qualifiedName);
                var qualifiedNameInCamelCase = toCamelCase(qualifiedName);
                var payload = {
                    path: "".concat(_path, ".").concat(qualifiedNameInCamelCase),
                    value: ""
                };
                hooks.call("modifyRmAttrPayload", this, qualifiedName, payload, componentsAlias2);
                if (_alias) {
                    var qualifiedNameAlias = _alias[qualifiedNameInCamelCase] || qualifiedName;
                    payload.path = "".concat(_path, ".").concat(toCamelCase(qualifiedNameAlias));
                }
                this.enqueueUpdate(payload);
                if (this.nodeName === VIEW) {
                    if (qualifiedNameInCamelCase === CATCHMOVE) {
                        this.enqueueUpdate({
                            path: "".concat(_path, ".", "nn"),
                            value: this.isAnyEventBinded() ? viewAlias : isHasExtractProp(this) ? staticViewAlias : pureViewAlias
                        });
                    } else if (isStaticView && !isHasExtractProp(this)) {
                        this.enqueueUpdate({
                            path: "".concat(_path, ".", "nn"),
                            value: pureViewAlias
                        });
                    }
                }
            }
        },
        {
            key: "getAttribute",
            value: function getAttribute(qualifiedName) {
                var attr = qualifiedName === STYLE ? this.style.cssText : this.props[qualifiedName];
                return attr !== null && attr !== void 0 ? attr : "";
            }
        },
        {
            key: "getElementsByTagName",
            value: function getElementsByTagName(tagName) {
                var _this = this;
                return treeToArray(this, function(el) {
                    return el.nodeName === tagName || tagName === "*" && _this !== el;
                });
            }
        },
        {
            key: "getElementsByClassName",
            value: function getElementsByClassName(className) {
                var classNames = className.trim().split(/\s+/);
                return treeToArray(this, function(el) {
                    var classList = el.classList;
                    return classNames.every(function(c) {
                        return classList.contains(c);
                    });
                });
            }
        },
        {
            key: "dispatchEvent",
            value: function dispatchEvent(event) {
                var cancelable = event.cancelable;
                var listeners = this.__handlers[event.type];
                if (!isArray(listeners)) {
                    return false;
                }
                for(var i = listeners.length; i--;){
                    var listener = listeners[i];
                    var result = void 0;
                    if (listener._stop) {
                        listener._stop = false;
                    } else {
                        hooks.call("modifyDispatchEvent", event, this);
                        result = listener.call(this, event);
                    }
                    if ((result === false || event._end) && cancelable) {
                        event.defaultPrevented = true;
                    }
                    if (!isUndefined(result) && event.mpEvent) {
                        var res = hooks.call("modifyTaroEventReturn", this, event, result);
                        if (res) {
                            event.mpEvent[EVENT_CALLBACK_RESULT] = result;
                        }
                    }
                    if (event._end && event._stop) {
                        break;
                    }
                }
                if (event._stop) {
                    this._stopPropagation(event);
                } else {
                    event._stop = true;
                }
                return listeners != null;
            }
        },
        {
            key: "addEventListener",
            value: function addEventListener(type, handler, options2) {
                var name = this.nodeName;
                var SPECIAL_NODES2 = hooks.call("getSpecialNodes");
                var sideEffect = true;
                if (isObject(options2) && options2.sideEffect === false) {
                    sideEffect = false;
                    delete options2.sideEffect;
                }
                hooks.call("modifyAddEventListener", this, sideEffect, getComponentsAlias2);
                if (sideEffect !== false && !this.isAnyEventBinded() && SPECIAL_NODES2.indexOf(name) > -1) {
                    var componentsAlias2 = getComponentsAlias2();
                    var alias = componentsAlias2[name]._num;
                    this.enqueueUpdate({
                        path: "".concat(this._path, ".", "nn"),
                        value: alias
                    });
                }
                _get(_get_prototype_of(_TaroElement.prototype), "addEventListener", this).call(this, type, handler, options2);
            }
        },
        {
            key: "removeEventListener",
            value: function removeEventListener(type, handler) {
                var sideEffect = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : true;
                _get(_get_prototype_of(_TaroElement.prototype), "removeEventListener", this).call(this, type, handler);
                var name = this.nodeName;
                var SPECIAL_NODES2 = hooks.call("getSpecialNodes");
                hooks.call("modifyRemoveEventListener", this, sideEffect, getComponentsAlias2);
                if (sideEffect !== false && !this.isAnyEventBinded() && SPECIAL_NODES2.indexOf(name) > -1) {
                    var componentsAlias2 = getComponentsAlias2();
                    var value = isHasExtractProp(this) ? "static-".concat(name) : "pure-".concat(name);
                    var valueAlias = componentsAlias2[value]._num;
                    this.enqueueUpdate({
                        path: "".concat(this._path, ".", "nn"),
                        value: valueAlias
                    });
                }
            }
        }
    ], [
        {
            key: "extend",
            value: function extend1(methodName, options2) {
                extend(_TaroElement, methodName, options2);
            }
        }
    ]);
    return _TaroElement;
}(TaroNode);
var options = {
    prerender: true,
    debug: false
};
function makeMap(str, expectsLowerCase) {
    var map = /* @__PURE__ */ Object.create(null);
    var list = str.split(",");
    for(var i = 0; i < list.length; i++){
        map[list[i]] = true;
    }
    return expectsLowerCase ? function(val) {
        return !!map[val.toLowerCase()];
    } : function(val) {
        return !!map[val];
    };
}
var internalCompsList = Object.keys(internalComponents).map(function(i) {
    return i.toLowerCase();
}).join(",");
var isMiniElements = makeMap(internalCompsList, true);
var isInlineElements = makeMap("a,i,abbr,iframe,select,acronym,slot,small,span,bdi,kbd,strong,big,map,sub,sup,br,mark,mark,meter,template,canvas,textarea,cite,object,time,code,output,u,data,picture,tt,datalist,var,dfn,del,q,em,s,embed,samp,b", true);
var isBlockElements = makeMap("address,fieldset,li,article,figcaption,main,aside,figure,nav,blockquote,footer,ol,details,form,p,dialog,h1,h2,h3,h4,h5,h6,pre,dd,header,section,div,hgroup,table,dl,hr,ul,dt", true);
options.html = {
    skipElements: /* @__PURE__ */ new Set([
        "style",
        "script"
    ]),
    voidElements: /* @__PURE__ */ new Set([
        "!doctype",
        "area",
        "base",
        "br",
        "col",
        "command",
        "embed",
        "hr",
        "img",
        "input",
        "keygen",
        "link",
        "meta",
        "param",
        "source",
        "track",
        "wbr"
    ]),
    closingElements: /* @__PURE__ */ new Set([
        "html",
        "head",
        "body",
        "p",
        "dt",
        "dd",
        "li",
        "option",
        "thead",
        "th",
        "tbody",
        "tr",
        "td",
        "tfoot",
        "colgroup"
    ]),
    renderHTMLTag: false
};
if (false) {
    if (ENABLE_INNER_HTML) {
        TaroNode.extend("innerHTML", {
            set: function set(html) {
                setInnerHTML.call(this, this, html);
            },
            get: function get() {
                return "";
            }
        });
        if (ENABLE_ADJACENT_HTML) {
            TaroNode.extend("insertAdjacentHTML", insertAdjacentHTML);
        }
    }
    if (ENABLE_CLONE_NODE) {
        TaroNode.extend("cloneNode", cloneNode);
    }
    if (ENABLE_CONTAINS) {
        TaroNode.extend("contains", contains);
    }
    if (ENABLE_SIZE_APIS) {
        TaroElement.extend("getBoundingClientRect", getBoundingClientRectImpl);
    }
    if (ENABLE_TEMPLATE_CONTENT) {
        TaroElement.extend("content", {
            get: function get() {
                return getTemplateContent(this);
            }
        });
    }
}
var TaroEvent = /*#__PURE__*/ function() {
    "use strict";
    function TaroEvent(type, opts, event) {
        _class_call_check(this, TaroEvent);
        this._stop = false;
        this._end = false;
        this.defaultPrevented = false;
        this.button = 0;
        this.timeStamp = Date.now();
        this.type = type.toLowerCase();
        this.mpEvent = event;
        this.bubbles = Boolean(opts && opts.bubbles);
        this.cancelable = Boolean(opts && opts.cancelable);
    }
    _create_class(TaroEvent, [
        {
            key: "stopPropagation",
            value: function stopPropagation() {
                this._stop = true;
            }
        },
        {
            key: "stopImmediatePropagation",
            value: function stopImmediatePropagation() {
                this._end = this._stop = true;
            }
        },
        {
            key: "preventDefault",
            value: function preventDefault() {
                this.defaultPrevented = true;
            }
        },
        {
            key: "target",
            get: function get() {
                var _$_a, _b, _c, _d, _e;
                var cacheTarget = this.cacheTarget;
                if (!cacheTarget) {
                    var target = Object.create(((_$_a = this.mpEvent) === null || _$_a === void 0 ? void 0 : _$_a.target) || null);
                    var currentEle = env.document.getElementById(((_b = target.dataset) === null || _b === void 0 ? void 0 : _b.sid) || target.id || null);
                    var element = env.document.getElementById(((_c = target.targetDataset) === null || _c === void 0 ? void 0 : _c.sid) || ((_d = target.dataset) === null || _d === void 0 ? void 0 : _d.sid) || target.id || null);
                    target.dataset = Object.assign(Object.assign({}, currentEle !== null ? currentEle.dataset : EMPTY_OBJ), element !== null ? element.dataset : EMPTY_OBJ);
                    for(var key in (_e = this.mpEvent) === null || _e === void 0 ? void 0 : _e.detail){
                        target[key] = this.mpEvent.detail[key];
                    }
                    this.cacheTarget = target;
                    return target;
                } else {
                    return cacheTarget;
                }
            }
        },
        {
            key: "currentTarget",
            get: function get() {
                var _$_a, _b, _c, _d, _e, _f, _g, _h;
                var cacheCurrentTarget = this.cacheCurrentTarget;
                if (!cacheCurrentTarget) {
                    var doc = env.document;
                    var currentTarget = Object.create(((_$_a = this.mpEvent) === null || _$_a === void 0 ? void 0 : _$_a.currentTarget) || null);
                    var element = doc.getElementById(((_b = currentTarget.dataset) === null || _b === void 0 ? void 0 : _b.sid) || currentTarget.id || null);
                    var targetElement = doc.getElementById(((_e = (_d = (_c = this.mpEvent) === null || _c === void 0 ? void 0 : _c.target) === null || _d === void 0 ? void 0 : _d.dataset) === null || _e === void 0 ? void 0 : _e.sid) || ((_g = (_f = this.mpEvent) === null || _f === void 0 ? void 0 : _f.target) === null || _g === void 0 ? void 0 : _g.id) || null);
                    if (element === null || element && element === targetElement) {
                        this.cacheCurrentTarget = this.target;
                        return this.target;
                    }
                    currentTarget.dataset = element.dataset;
                    for(var key in (_h = this.mpEvent) === null || _h === void 0 ? void 0 : _h.detail){
                        currentTarget[key] = this.mpEvent.detail[key];
                    }
                    this.cacheCurrentTarget = currentTarget;
                    return currentTarget;
                } else {
                    return cacheCurrentTarget;
                }
            }
        }
    ]);
    return TaroEvent;
}();
function createEvent(event, node) {
    if (typeof event === "string") {
        return new TaroEvent(event, {
            bubbles: true,
            cancelable: true
        });
    }
    var domEv = new TaroEvent(event.type, {
        bubbles: true,
        cancelable: true
    }, event);
    for(var key in event){
        if (key === CURRENT_TARGET || key === TARGET || key === TYPE || key === TIME_STAMP) {
            continue;
        } else {
            domEv[key] = event[key];
        }
    }
    if (domEv.type === CONFIRM && (node === null || node === void 0 ? void 0 : node.nodeName) === INPUT) {
        domEv[KEY_CODE] = 13;
    }
    return domEv;
}
var eventsBatch = {};
function getEventCBResult(event) {
    var result = event[EVENT_CALLBACK_RESULT];
    if (!isUndefined(result)) {
        delete event[EVENT_CALLBACK_RESULT];
    }
    return result;
}
function eventHandler(event) {
    var _$_a, _b;
    event.type === void 0 && Object.defineProperty(event, "type", {
        value: event._type
    });
    event.detail === void 0 && Object.defineProperty(event, "detail", {
        value: event._detail || Object.assign({}, event)
    });
    event.currentTarget = event.currentTarget || event.target || Object.assign({}, event);
    hooks.call("modifyMpEventImpl", event);
    var currentTarget = event.currentTarget;
    var id = ((_$_a = currentTarget.dataset) === null || _$_a === void 0 ? void 0 : _$_a.sid) || currentTarget.id || ((_b = event.detail) === null || _b === void 0 ? void 0 : _b.id) || "";
    var node = env.document.getElementById(id);
    if (node) {
        var dispatch = function() {
            var e = createEvent(event, node);
            hooks.call("modifyTaroEvent", e, node);
            hooks.call("dispatchTaroEvent", e, node);
            hooks.call("dispatchTaroEventFinish", e, node);
        };
        if (hooks.isExist("batchedEventUpdates")) {
            var type = event.type;
            if (!hooks.call("isBubbleEvents", type) || !isParentBinded(node, type) || type === TOUCHMOVE && !!node.props.catchMove) {
                hooks.call("batchedEventUpdates", function() {
                    if (eventsBatch[type]) {
                        eventsBatch[type].forEach(function(fn) {
                            return fn();
                        });
                        delete eventsBatch[type];
                    }
                    dispatch();
                });
                return getEventCBResult(event);
            } else {
                (eventsBatch[type] || (eventsBatch[type] = [])).push(dispatch);
            }
        } else {
            dispatch();
            return getEventCBResult(event);
        }
    }
}
var FormElement = /*#__PURE__*/ function(TaroElement) {
    "use strict";
    _inherits(FormElement, TaroElement);
    var _super = _create_super(FormElement);
    function FormElement() {
        _class_call_check(this, FormElement);
        return _super.apply(this, arguments);
    }
    _create_class(FormElement, [
        {
            key: "type",
            get: function get() {
                var _$_a;
                return (_$_a = this.props[TYPE]) !== null && _$_a !== void 0 ? _$_a : "";
            },
            set: function set(val) {
                this.setAttribute(TYPE, val);
            }
        },
        {
            key: "value",
            get: function get() {
                var val = this.props[VALUE];
                return val == null ? "" : val;
            },
            set: function set(val) {
                this.setAttribute(VALUE, val);
            }
        },
        {
            key: "dispatchEvent",
            value: function dispatchEvent(event) {
                if (event.mpEvent) {
                    var val = event.mpEvent.detail.value;
                    if (event.type === CHANGE) {
                        this.props.value = val;
                    } else if (event.type === INPUT) {
                        this.value = val;
                    }
                }
                return _get(_get_prototype_of(FormElement.prototype), "dispatchEvent", this).call(this, event);
            }
        }
    ]);
    return FormElement;
}(TaroElement);
var Performance = /*#__PURE__*/ function() {
    "use strict";
    function Performance() {
        _class_call_check(this, Performance);
        this.recorder = /* @__PURE__ */ new Map();
    }
    _create_class(Performance, [
        {
            key: "start",
            value: function start(id) {
                if (!options.debug) {
                    return;
                }
                this.recorder.set(id, Date.now());
            }
        },
        {
            key: "stop",
            value: function stop(id) {
                if (!options.debug) {
                    return;
                }
                var now2 = Date.now();
                var prev = this.recorder.get(id);
                this.recorder.delete(id);
                var time = now2 - prev;
                console.log("".concat(id, " 时长： ").concat(time, "ms"));
            }
        }
    ]);
    return Performance;
}();
var perf = new Performance();
function findCustomWrapper(root, dataPathArr) {
    var list = dataPathArr.slice(1);
    var currentData = root;
    var customWrapper;
    var splitedPath = "";
    list.some(function(item, i) {
        var key = item.replace(/^\[(.+)\]$/, "$1").replace(/\bcn\b/g, "childNodes");
        currentData = currentData[key];
        if (isArray(currentData)) {
            currentData = currentData.filter(function(el) {
                return !isComment(el);
            });
        }
        if (isUndefined(currentData)) return true;
        if (currentData.nodeName === CUSTOM_WRAPPER) {
            var res = customWrapperCache.get(currentData.sid);
            if (res) {
                customWrapper = res;
                splitedPath = dataPathArr.slice(i + 2).join(".");
            }
        }
    });
    if (customWrapper) {
        return {
            customWrapper: customWrapper,
            splitedPath: splitedPath
        };
    }
}
var TaroRootElement = /*#__PURE__*/ function(TaroElement) {
    "use strict";
    _inherits(TaroRootElement, TaroElement);
    var _super = _create_super(TaroRootElement);
    function TaroRootElement() {
        _class_call_check(this, TaroRootElement);
        var _this;
        _this = _super.call(this);
        _this.updatePayloads = [];
        _this.updateCallbacks = [];
        _this.pendingUpdate = false;
        _this.ctx = null;
        _this.nodeName = ROOT_STR;
        _this.tagName = ROOT_STR.toUpperCase();
        return _this;
    }
    _create_class(TaroRootElement, [
        {
            key: "_path",
            get: function get() {
                return ROOT_STR;
            }
        },
        {
            key: "_root",
            get: function get() {
                return this;
            }
        },
        {
            key: "enqueueUpdate",
            value: function enqueueUpdate(payload) {
                this.updatePayloads.push(payload);
                if (!this.pendingUpdate && this.ctx) {
                    this.performUpdate();
                }
            }
        },
        {
            key: "performUpdate",
            value: function performUpdate() {
                var initRender = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false, prerender = arguments.length > 1 ? arguments[1] : void 0;
                var _this = this;
                this.pendingUpdate = true;
                var ctx = hooks.call("proxyToRaw", this.ctx);
                setTimeout(function() {
                    var _loop = function(path1) {
                        resetPaths.forEach(function(p) {
                            if (path1.includes(p) && path1 !== p) {
                                delete data[path1];
                            }
                        });
                        var value = data[path1];
                        if (isFunction(value)) {
                            data[path1] = value();
                        }
                    };
                    var setDataMark = "".concat(SET_DATA, " 开始时间戳 ").concat(Date.now());
                    perf.start(setDataMark);
                    var data = /* @__PURE__ */ Object.create(null);
                    var resetPaths = new Set(initRender ? [
                        "root.cn.[0]",
                        "root.cn[0]"
                    ] : []);
                    while(_this.updatePayloads.length > 0){
                        var _this_updatePayloads_shift = _this.updatePayloads.shift(), path = _this_updatePayloads_shift.path, value = _this_updatePayloads_shift.value;
                        if (path.endsWith("cn")) {
                            resetPaths.add(path);
                        }
                        data[path] = value;
                    }
                    for(var path1 in data)_loop(path1);
                    if (isFunction(prerender)) return prerender(data);
                    _this.pendingUpdate = false;
                    var normalUpdate = {};
                    var customWrapperMap = /* @__PURE__ */ new Map();
                    if (initRender) {
                        normalUpdate = data;
                    } else {
                        for(var p in data){
                            var dataPathArr = p.split(".");
                            var found = findCustomWrapper(_this, dataPathArr);
                            if (found) {
                                var customWrapper = found.customWrapper, splitedPath = found.splitedPath;
                                customWrapperMap.set(customWrapper, Object.assign(Object.assign({}, customWrapperMap.get(customWrapper) || {}), _define_property({}, "i.".concat(splitedPath), data[p])));
                            } else {
                                normalUpdate[p] = data[p];
                            }
                        }
                    }
                    var customWrapperCount = customWrapperMap.size;
                    var isNeedNormalUpdate = Object.keys(normalUpdate).length > 0;
                    var updateArrLen = customWrapperCount + (isNeedNormalUpdate ? 1 : 0);
                    var executeTime = 0;
                    var cb = function() {
                        if (++executeTime === updateArrLen) {
                            perf.stop(setDataMark);
                            _this.flushUpdateCallback();
                            initRender && perf.stop(PAGE_INIT);
                        }
                    };
                    if (customWrapperCount) {
                        customWrapperMap.forEach(function(data2, ctx2) {
                            if (options.debug) {
                                console.log("custom wrapper setData: ", data2);
                            }
                            ctx2.setData(data2, cb);
                        });
                    }
                    if (isNeedNormalUpdate) {
                        if (options.debug) {
                            console.log("page setData:", normalUpdate);
                        }
                        ctx.setData(normalUpdate, cb);
                    }
                }, 0);
            }
        },
        {
            key: "enqueueUpdateCallback",
            value: function enqueueUpdateCallback(cb, ctx) {
                this.updateCallbacks.push(function() {
                    ctx ? cb.call(ctx) : cb();
                });
            }
        },
        {
            key: "flushUpdateCallback",
            value: function flushUpdateCallback() {
                var updateCallbacks = this.updateCallbacks;
                if (!updateCallbacks.length) return;
                var copies = updateCallbacks.slice(0);
                this.updateCallbacks.length = 0;
                for(var i = 0; i < copies.length; i++){
                    copies[i]();
                }
            }
        }
    ]);
    return TaroRootElement;
}(TaroElement);
var TaroText = /*#__PURE__*/ function(TaroNode) {
    "use strict";
    _inherits(TaroText, TaroNode);
    var _super = _create_super(TaroText);
    function TaroText(value) {
        _class_call_check(this, TaroText);
        var _this;
        _this = _super.call(this);
        _this.nodeType = 3;
        _this.nodeName = "#text";
        _this._value = value;
        return _this;
    }
    _create_class(TaroText, [
        {
            key: "textContent",
            get: function get() {
                return this._value;
            },
            set: function set(text) {
                MutationObserver$1.record({
                    target: this,
                    type: "characterData",
                    oldValue: this._value
                });
                this._value = text;
                this.enqueueUpdate({
                    path: "".concat(this._path, ".", "v"),
                    value: text
                });
            }
        },
        {
            key: "nodeValue",
            get: function get() {
                return this._value;
            },
            set: function set(text) {
                this.textContent = text;
            }
        },
        {
            key: "data",
            get: function get() {
                return this._value;
            },
            set: function set(text) {
                this.textContent = text;
            }
        }
    ]);
    return TaroText;
}(TaroNode);
var URLSearchParams = true ? env.window.URLSearchParams : (_a = /*#__PURE__*/ function() {
    "use strict";
    function _a1(query) {
        _class_call_check(this, _a1);
        _dict.set(this, /* @__PURE__ */ Object.create(null));
        query !== null && query !== void 0 ? query : query = "";
        var dict = __classPrivateFieldGet(this, _dict, "f");
        if (typeof query === "string") {
            if (query.charAt(0) === "?") {
                query = query.slice(1);
            }
            for(var pairs = query.split("&"), i = 0, length = pairs.length; i < length; i++){
                var value = pairs[i];
                var index = value.indexOf("=");
                try {
                    if (index > -1) {
                        appendTo(dict, decode(value.slice(0, index)), decode(value.slice(index + 1)));
                    } else if (value.length) {
                        appendTo(dict, decode(value), "");
                    }
                } catch (err) {
                    if (true) {
                        console.warn("[Taro warn] URL 参数 ".concat(value, " decode 异常"));
                    }
                }
            }
        } else {
            if (isArray(query)) {
                for(var i1 = 0, length1 = query.length; i1 < length1; i1++){
                    var value1 = query[i1];
                    appendTo(dict, value1[0], value1[1]);
                }
            } else if (query.forEach) {
                query.forEach(addEach, dict);
            } else {
                for(var key in query){
                    appendTo(dict, key, query[key]);
                }
            }
        }
    }
    _create_class(_a1, [
        {
            key: "append",
            value: function append(name, value) {
                appendTo(__classPrivateFieldGet(this, _dict, "f"), name, value);
            }
        },
        {
            key: "delete",
            value: function _delete(name) {
                delete __classPrivateFieldGet(this, _dict, "f")[name];
            }
        },
        {
            key: "get",
            value: function get(name) {
                var dict = __classPrivateFieldGet(this, _dict, "f");
                return name in dict ? dict[name][0] : null;
            }
        },
        {
            key: "getAll",
            value: function getAll(name) {
                var dict = __classPrivateFieldGet(this, _dict, "f");
                return name in dict ? dict[name].slice(0) : [];
            }
        },
        {
            key: "has",
            value: function has(name) {
                return name in __classPrivateFieldGet(this, _dict, "f");
            }
        },
        {
            key: "keys",
            value: function keys() {
                return Object.keys(__classPrivateFieldGet(this, _dict, "f"));
            }
        },
        {
            key: "set",
            value: function set(name, value) {
                __classPrivateFieldGet(this, _dict, "f")[name] = [
                    "" + value
                ];
            }
        },
        {
            key: "forEach",
            value: function forEach(callback, thisArg) {
                var dict = __classPrivateFieldGet(this, _dict, "f");
                Object.getOwnPropertyNames(dict).forEach(function(name) {
                    dict[name].forEach(function(value) {
                        callback.call(thisArg, value, name, this);
                    }, this);
                }, this);
            }
        },
        {
            key: "toJSON",
            value: function toJSON() {
                return {};
            }
        },
        {
            key: "toString",
            value: function toString() {
                var dict = __classPrivateFieldGet(this, _dict, "f");
                var query = [];
                for(var key in dict){
                    var name = encode(key);
                    for(var i = 0, value = dict[key]; i < value.length; i++){
                        query.push(name + "=" + encode(value[i]));
                    }
                }
                return query.join("&");
            }
        }
    ]);
    return _a1;
}(), _dict = /* @__PURE__ */ new WeakMap(), _a);
var _TaroURL_hash;
var _TaroURL_hostname;
var _TaroURL_pathname;
var _TaroURL_port;
var _TaroURL_protocol;
var _TaroURL_search;
_TaroURL_hash = /* @__PURE__ */ new WeakMap(), _TaroURL_hostname = /* @__PURE__ */ new WeakMap(), _TaroURL_pathname = /* @__PURE__ */ new WeakMap(), _TaroURL_port = /* @__PURE__ */ new WeakMap(), _TaroURL_protocol = /* @__PURE__ */ new WeakMap(), _TaroURL_search = /* @__PURE__ */ new WeakMap();
var URL = true ? env.window.URL : TaroURL;
function parseUrl() {
    var url = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : "";
    var result = {
        href: "",
        origin: "",
        protocol: "",
        hostname: "",
        host: "",
        port: "",
        pathname: "",
        search: "",
        hash: ""
    };
    if (!url || !isString(url)) return result;
    url = url.trim();
    var PATTERN = /^(([^:/?#]+):)?\/\/(([^/?#]+):(.+)@)?([^/?#:]*)(:(\d+))?([^?#]*)(\?([^#]*))?(#(.*))?/;
    var matches = url.match(PATTERN);
    if (!matches) return result;
    result.protocol = matches[1] || "https:";
    result.hostname = matches[6] || "taro.com";
    result.port = matches[8] || "";
    result.pathname = matches[9] || "/";
    result.search = matches[10] || "";
    result.hash = matches[12] || "";
    result.href = url;
    result.origin = result.protocol + "//" + result.hostname;
    result.host = result.hostname + (result.port ? ":".concat(result.port) : "");
    return result;
}
var document$1 = true ? env.document : env.document = createDocument();
var getComputedStyle = true ? env.window.getComputedStyle : function getComputedStyle(element) {
    return element.style;
};
var eventCenter = hooks.call("getEventCenter", Events);
var RuntimeCache = /*#__PURE__*/ function() {
    "use strict";
    function RuntimeCache(name) {
        _class_call_check(this, RuntimeCache);
        this.cache = /* @__PURE__ */ new Map();
        this.name = name;
    }
    _create_class(RuntimeCache, [
        {
            key: "has",
            value: function has(identifier) {
                return this.cache.has(identifier);
            }
        },
        {
            key: "set",
            value: function set(identifier, ctx) {
                if (identifier && ctx) {
                    this.cache.set(identifier, ctx);
                }
            }
        },
        {
            key: "get",
            value: function get(identifier) {
                if (this.has(identifier)) return this.cache.get(identifier);
            }
        },
        {
            key: "delete",
            value: function _delete(identifier) {
                this.cache.delete(identifier);
            }
        }
    ]);
    return RuntimeCache;
}();
var _TaroHistory_instances;
var _TaroHistory_location;
var _TaroHistory_stack;
var _TaroHistory_cur;
var _TaroHistory_window;
var _TaroHistory_reset;
var cache$1 = new RuntimeCache("history");
_TaroHistory_location = /* @__PURE__ */ new WeakMap(), _TaroHistory_stack = /* @__PURE__ */ new WeakMap(), _TaroHistory_cur = /* @__PURE__ */ new WeakMap(), _TaroHistory_window = /* @__PURE__ */ new WeakMap(), _TaroHistory_instances = /* @__PURE__ */ new WeakSet(), _TaroHistory_reset = function _TaroHistory_reset2() {
    var href = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : "";
    __classPrivateFieldSet(this, _TaroHistory_stack, [
        {
            state: null,
            title: "",
            url: href || __classPrivateFieldGet(this, _TaroHistory_location, "f").href
        }
    ], "f");
    __classPrivateFieldSet(this, _TaroHistory_cur, 0, "f");
};
var History = true ? env.window.History : TaroHistory;
var Current = {
    app: null,
    router: null,
    page: null
};
var getCurrentInstance = function() {
    return Current;
};
var _TaroLocation_instances;
var _TaroLocation_url;
var _TaroLocation_noCheckUrl;
var _TaroLocation_window;
var _TaroLocation_reset;
var _TaroLocation_getPreValue;
var _TaroLocation_rollBack;
var _TaroLocation_recordHistory;
var _TaroLocation_checkUrlChange;
var INIT_URL = "https://taro.com";
var cache = new RuntimeCache("location");
_TaroLocation_url = /* @__PURE__ */ new WeakMap(), _TaroLocation_noCheckUrl = /* @__PURE__ */ new WeakMap(), _TaroLocation_window = /* @__PURE__ */ new WeakMap(), _TaroLocation_instances = /* @__PURE__ */ new WeakSet(), _TaroLocation_reset = function _TaroLocation_reset2() {
    var Current2 = getCurrentInstance();
    var router = Current2.router;
    if (router) {
        var path = router.path, params = router.params;
        var searchArr = Object.keys(params).map(function(key) {
            return "".concat(key, "=").concat(params[key]);
        });
        var searchStr = searchArr.length > 0 ? "?" + searchArr.join("&") : "";
        var url = "".concat(INIT_URL).concat(path.startsWith("/") ? path : "/" + path).concat(searchStr);
        __classPrivateFieldSet(this, _TaroLocation_url, new URL(url), "f");
        this.trigger("__reset_history__", this.href);
    }
}, _TaroLocation_getPreValue = function _TaroLocation_getPreValue2() {
    return __classPrivateFieldGet(this, _TaroLocation_url, "f")._toRaw();
}, _TaroLocation_rollBack = function _TaroLocation_rollBack2(href) {
    __classPrivateFieldGet(this, _TaroLocation_url, "f").href = href;
}, _TaroLocation_recordHistory = function _TaroLocation_recordHistory2() {
    this.trigger("__record_history__", this.href);
}, _TaroLocation_checkUrlChange = function _TaroLocation_checkUrlChange2(preValue) {
    if (__classPrivateFieldGet(this, _TaroLocation_noCheckUrl, "f")) {
        return false;
    }
    var __classPrivateFieldGet__toRaw = __classPrivateFieldGet(this, _TaroLocation_url, "f")._toRaw(), protocol = __classPrivateFieldGet__toRaw.protocol, hostname = __classPrivateFieldGet__toRaw.hostname, port = __classPrivateFieldGet__toRaw.port, pathname = __classPrivateFieldGet__toRaw.pathname, search = __classPrivateFieldGet__toRaw.search, hash = __classPrivateFieldGet__toRaw.hash;
    if (protocol !== preValue.protocol || hostname !== preValue.hostname || port !== preValue.port) {
        __classPrivateFieldGet(this, _TaroLocation_instances, "m", _TaroLocation_rollBack).call(this, preValue.href);
        return false;
    }
    if (pathname !== preValue.pathname) {
        return true;
    }
    if (search !== preValue.search) {
        return true;
    }
    if (hash !== preValue.hash) {
        __classPrivateFieldGet(this, _TaroLocation_window, "f").trigger("hashchange");
        return true;
    }
    __classPrivateFieldGet(this, _TaroLocation_instances, "m", _TaroLocation_rollBack).call(this, preValue.href);
    return false;
};
var Location = true ? env.window.Location : TaroLocation;
var machine = "Macintosh";
var arch = "Intel Mac OS X 10_14_5";
var engine = "AppleWebKit/534.36 (KHTML, like Gecko) NodeJS/v4.1.0 Chrome/76.0.3809.132 Safari/534.36";
var msg = "(" + machine + "; " + arch + ") " + engine;
var nav = true ? env.window.navigator : {
    appCodeName: "Mozilla",
    appName: "Netscape",
    appVersion: "5.0 " + msg,
    cookieEnabled: true,
    mimeTypes: [],
    onLine: true,
    platform: "MacIntel",
    plugins: [],
    product: "Taro",
    productSub: "20030107",
    userAgent: "Mozilla/5.0 " + msg,
    vendor: "Joyent",
    vendorSub: ""
};
var now;
(function() {
    var loadTime;
    if (typeof performance !== "undefined" && performance !== null && performance.now) {
        now = function() {
            return performance.now();
        };
    } else if (Date.now) {
        loadTime = Date.now();
        now = function() {
            return Date.now() - loadTime;
        };
    } else {
        loadTime = /* @__PURE__ */ new Date().getTime();
        now = function() {
            return /* @__PURE__ */ new Date().getTime() - loadTime;
        };
    }
})();
var lastTime = 0;
var _raf = typeof requestAnimationFrame !== "undefined" && requestAnimationFrame !== null ? requestAnimationFrame : function _raf(callback) {
    var _now = now();
    var nextTime = Math.max(lastTime + 16, _now);
    return setTimeout(function() {
        callback(lastTime = nextTime);
    }, nextTime - _now);
};
var _caf = typeof cancelAnimationFrame !== "undefined" && cancelAnimationFrame !== null ? cancelAnimationFrame : function _caf(seed) {
    clearTimeout(seed);
};
var window$1 = true ? env.window : env.window = new TaroWindow();
var location = window$1.location;
var history = window$1.history;
var SVGElement = /*#__PURE__*/ function(TaroElement) {
    "use strict";
    _inherits(SVGElement, TaroElement);
    var _super = _create_super(SVGElement);
    function SVGElement() {
        _class_call_check(this, SVGElement);
        return _super.apply(this, arguments);
    }
    return SVGElement;
}(TaroElement);
var addLeadingSlash = function() {
    var url = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : "";
    return url.charAt(0) === "/" ? url : "/" + url;
};
var hasBasename = function() {
    var path = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : "", prefix = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : "";
    return new RegExp("^" + prefix + "(\\/|\\?|#|$)", "i").test(path) || path === prefix;
};
var stripBasename = function() {
    var path = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : "", prefix = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : "";
    return hasBasename(path, prefix) ? path.substring(prefix.length) : path;
};
var stripTrailing = function() {
    var str = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : "";
    return str.replace(/[?#][\s\S]*$/, "");
};
var stripSuffix = function() {
    var path = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : "", suffix = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : "";
    return path.includes(suffix) ? path.substring(0, path.length - suffix.length) : path;
};
var getHomePage = function() {
    var path = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : "", basename = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : "", customRoutes = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {}, entryPagePath = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : "";
    var _$_a;
    var routePath = addLeadingSlash(stripBasename(path, basename));
    var alias = ((_$_a = Object.entries(customRoutes).find(function(param) {
        var _param = _sliced_to_array(param, 1), key = _param[0];
        return key === routePath;
    })) === null || _$_a === void 0 ? void 0 : _$_a[1]) || routePath;
    return entryPagePath || (typeof alias === "string" ? alias : alias[0]) || basename;
};
var getCurrentPage = function() {
    var routerMode = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : "hash", basename = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : "/";
    var pagePath = routerMode === "hash" ? location.hash.slice(1).split("?")[0] : location.pathname;
    return addLeadingSlash(stripBasename(pagePath, basename));
};
var instances = /* @__PURE__ */ new Map();
var pageId = incrementId();
function injectPageInstance(inst, id) {
    hooks.call("mergePageInstance", instances.get(id), inst);
    instances.set(id, inst);
}
function getPageInstance(id) {
    return instances.get(id);
}
function removePageInstance(id) {
    instances.delete(id);
}
function safeExecute(path, lifecycle) {
    for(var _len = arguments.length, args = new Array(_len > 2 ? _len - 2 : 0), _key = 2; _key < _len; _key++){
        args[_key - 2] = arguments[_key];
    }
    var instance = instances.get(path);
    if (instance == null) {
        return;
    }
    var func = hooks.call("getLifecycle", instance, lifecycle);
    if (isArray(func)) {
        var res = func.map(function(fn) {
            return fn.apply(instance, args);
        });
        return res[0];
    }
    if (!isFunction(func)) {
        return;
    }
    return func.apply(instance, args);
}
function stringify(obj) {
    if (obj == null) {
        return "";
    }
    var path = Object.keys(obj).map(function(key) {
        return key + "=" + obj[key];
    }).join("&");
    return path === "" ? path : "?" + path;
}
function getPath(id, options2) {
    var idx = id.indexOf("?");
    if (true) {
        return "".concat(idx > -1 ? id.substring(0, idx) : id).concat(stringify((options2 === null || options2 === void 0 ? void 0 : options2.stamp) ? {
            stamp: options2.stamp
        } : {}));
    } else {
        return "".concat(idx > -1 ? id.substring(0, idx) : id).concat(stringify(options2));
    }
}
function getOnReadyEventKey(path) {
    return path + "." + ON_READY;
}
function getOnShowEventKey(path) {
    return path + "." + ON_SHOW;
}
function getOnHideEventKey(path) {
    return path + "." + ON_HIDE;
}
function createPageConfig(component, pageName, data, pageConfig) {
    var id = pageName !== null && pageName !== void 0 ? pageName : "taro_page_".concat(pageId());
    var _hooks_call_page = _sliced_to_array(hooks.call("getMiniLifecycleImpl").page, 7), ONLOAD = _hooks_call_page[0], ONUNLOAD = _hooks_call_page[1], ONREADY = _hooks_call_page[2], ONSHOW = _hooks_call_page[3], ONHIDE = _hooks_call_page[4], LIFECYCLES = _hooks_call_page[5], SIDE_EFFECT_LIFECYCLES = _hooks_call_page[6];
    var pageElement = null;
    var unmounting = false;
    var prepareMountList = [];
    function setCurrentRouter(page) {
        var router = true ? page.$taroPath : page.route || page.__route__ || page.$taroPath;
        Current.router = {
            params: page.$taroParams,
            path: addLeadingSlash(router),
            $taroPath: page.$taroPath,
            onReady: getOnReadyEventKey(id),
            onShow: getOnShowEventKey(id),
            onHide: getOnHideEventKey(id)
        };
        if (!isUndefined(page.exitState)) {
            Current.router.exitState = page.exitState;
        }
    }
    var loadResolver;
    var hasLoaded;
    var _obj;
    var config = (_obj = {}, _define_property(_obj, ONLOAD, function() {
        var options2 = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {}, cb = arguments.length > 1 ? arguments[1] : void 0;
        var _this = this;
        hasLoaded = new Promise(function(resolve) {
            loadResolver = resolve;
        });
        perf.start(PAGE_INIT);
        Current.page = this;
        this.config = pageConfig || {};
        var uniqueOptions = Object.assign({}, options2, {
            $taroTimestamp: Date.now()
        });
        var $taroPath = this.$taroPath = getPath(id, uniqueOptions);
        if (true) {
            config.path = $taroPath;
        }
        if (this.$taroParams == null) {
            this.$taroParams = uniqueOptions;
        }
        setCurrentRouter(this);
        if (false) {
            window$1.trigger(CONTEXT_ACTIONS.INIT, $taroPath);
        }
        var mount = function() {
            Current.app.mount(component, $taroPath, function() {
                pageElement = env.document.getElementById($taroPath);
                ensure(pageElement !== null, "没有找到页面实例。");
                safeExecute($taroPath, ON_LOAD, _this.$taroParams);
                loadResolver();
                if (false) {
                    pageElement.ctx = _this;
                    pageElement.performUpdate(true, cb);
                } else {
                    isFunction(cb) && cb();
                }
            });
        };
        if (unmounting) {
            prepareMountList.push(mount);
        } else {
            mount();
        }
    }), _define_property(_obj, ONUNLOAD, function() {
        var $taroPath = this.$taroPath;
        if (false) {
            window$1.trigger(CONTEXT_ACTIONS.DESTORY, $taroPath);
        }
        safeExecute($taroPath, ONUNLOAD);
        unmounting = true;
        Current.app.unmount($taroPath, function() {
            unmounting = false;
            instances.delete($taroPath);
            if (pageElement) {
                pageElement.ctx = null;
                pageElement = null;
            }
            if (prepareMountList.length) {
                prepareMountList.forEach(function(fn) {
                    return fn();
                });
                prepareMountList = [];
            }
        });
    }), _define_property(_obj, ONREADY, function() {
        var _this = this;
        hasLoaded.then(function() {
            safeExecute(_this.$taroPath, ON_READY);
            _raf(function() {
                return eventCenter.trigger(getOnReadyEventKey(id));
            });
            _this.onReady.called = true;
        });
    }), _define_property(_obj, ONSHOW, function() {
        var options2 = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
        var _this = this;
        hasLoaded.then(function() {
            Current.page = _this;
            setCurrentRouter(_this);
            if (false) {
                window$1.trigger(CONTEXT_ACTIONS.RECOVER, _this.$taroPath);
            }
            safeExecute(_this.$taroPath, ON_SHOW, options2);
            _raf(function() {
                return eventCenter.trigger(getOnShowEventKey(id));
            });
        });
    }), _define_property(_obj, ONHIDE, function() {
        if (false) {
            window$1.trigger(CONTEXT_ACTIONS.RESTORE, this.$taroPath);
        }
        if (Current.page === this) {
            Current.page = null;
            Current.router = null;
        }
        safeExecute(this.$taroPath, ON_HIDE);
        eventCenter.trigger(getOnHideEventKey(id));
    }), _obj);
    if (true) {
        config.getOpenerEventChannel = function() {
            return EventChannel.pageChannel;
        };
    }
    LIFECYCLES.forEach(function(lifecycle) {
        var isDefer = false;
        lifecycle = lifecycle.replace(/^defer:/, function() {
            isDefer = true;
            return "";
        });
        config[lifecycle] = function() {
            var _this = this, _arguments = arguments;
            var exec = function() {
                return safeExecute.apply(void 0, [
                    _this.$taroPath,
                    lifecycle
                ].concat(Array.prototype.slice.call(_arguments)));
            };
            if (isDefer) {
                hasLoaded.then(exec);
            } else {
                return exec();
            }
        };
    });
    SIDE_EFFECT_LIFECYCLES.forEach(function(lifecycle) {
        var _$_a;
        if (component[lifecycle] || ((_$_a = component.prototype) === null || _$_a === void 0 ? void 0 : _$_a[lifecycle]) || component[lifecycle.replace(/^on/, "enable")] || (pageConfig === null || pageConfig === void 0 ? void 0 : pageConfig[lifecycle.replace(/^on/, "enable")])) {
            config[lifecycle] = function() {
                for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){
                    args[_key] = arguments[_key];
                }
                var _a2;
                var target = (_a2 = args[0]) === null || _a2 === void 0 ? void 0 : _a2.target;
                if (target === null || target === void 0 ? void 0 : target.id) {
                    var id2 = target.id;
                    var element = env.document.getElementById(id2);
                    if (element) {
                        target.dataset = element.dataset;
                    }
                }
                return safeExecute.apply(void 0, [
                    this.$taroPath,
                    lifecycle
                ].concat(_to_consumable_array(args)));
            };
        }
    });
    config.eh = eventHandler;
    if (!isUndefined(data)) {
        config.data = data;
    }
    hooks.call("modifyPageObject", config);
    return config;
}
function createComponentConfig(component, componentName, data) {
    var id = componentName !== null && componentName !== void 0 ? componentName : "taro_component_".concat(pageId());
    var componentElement = null;
    var _hooks_call_component = _sliced_to_array(hooks.call("getMiniLifecycleImpl").component, 2), ATTACHED = _hooks_call_component[0], DETACHED = _hooks_call_component[1];
    var _obj;
    var config = (_obj = {}, _define_property(_obj, ATTACHED, function() {
        var _this = this;
        var _$_a;
        perf.start(PAGE_INIT);
        this.pageIdCache = ((_$_a = this.getPageId) === null || _$_a === void 0 ? void 0 : _$_a.call(this)) || pageId();
        var path = getPath(id, {
            id: this.pageIdCache
        });
        Current.app.mount(component, path, function() {
            componentElement = env.document.getElementById(path);
            ensure(componentElement !== null, "没有找到组件实例。");
            _this.$taroInstances = instances.get(path);
            safeExecute(path, ON_LOAD);
            if (false) {
                componentElement.ctx = _this;
                componentElement.performUpdate(true);
            }
        });
    }), _define_property(_obj, DETACHED, function() {
        var path = getPath(id, {
            id: this.pageIdCache
        });
        Current.app.unmount(path, function() {
            instances.delete(path);
            if (componentElement) {
                componentElement.ctx = null;
            }
        });
    }), _define_property(_obj, "methods", {
        eh: eventHandler
    }), _obj);
    if (!isUndefined(data)) {
        config.data = data;
    }
    [
        OPTIONS,
        EXTERNAL_CLASSES,
        BEHAVIORS
    ].forEach(function(key) {
        var _$_a;
        config[key] = (_$_a = component[key]) !== null && _$_a !== void 0 ? _$_a : EMPTY_OBJ;
    });
    return config;
}
function createRecursiveComponentConfig(componentName) {
    var isCustomWrapper = componentName === CUSTOM_WRAPPER;
    var _hooks_call_component = _sliced_to_array(hooks.call("getMiniLifecycleImpl").component, 2), ATTACHED = _hooks_call_component[0], DETACHED = _hooks_call_component[1];
    var _obj;
    var lifeCycles = isCustomWrapper ? (_obj = {}, _define_property(_obj, ATTACHED, function() {
        var _$_a, _b;
        var componentId = ((_$_a = this.data.i) === null || _$_a === void 0 ? void 0 : _$_a.sid) || ((_b = this.props.i) === null || _b === void 0 ? void 0 : _b.sid);
        if (isString(componentId)) {
            customWrapperCache.set(componentId, this);
            var el = env.document.getElementById(componentId);
            if (el) {
                el.ctx = this;
            }
        }
    }), _define_property(_obj, DETACHED, function() {
        var _$_a, _b;
        var componentId = ((_$_a = this.data.i) === null || _$_a === void 0 ? void 0 : _$_a.sid) || ((_b = this.props.i) === null || _b === void 0 ? void 0 : _b.sid);
        if (isString(componentId)) {
            customWrapperCache.delete(componentId);
            var el = env.document.getElementById(componentId);
            if (el) {
                el.ctx = null;
            }
        }
    }), _obj) : EMPTY_OBJ;
    return hooks.call("modifyRecursiveComponentConfig", Object.assign({
        properties: {
            i: {
                type: Object,
                value: _define_property({}, "nn", getComponentsAlias(internalComponents)[VIEW]._num)
            },
            l: {
                type: String,
                value: ""
            }
        },
        options: {
            addGlobalClass: true,
            virtualHost: !isCustomWrapper
        },
        methods: {
            eh: eventHandler
        }
    }, lifeCycles), {
        isCustomWrapper: isCustomWrapper
    });
}
var TIMEOUT = 100;
var nextTick = function(cb, ctx) {
    var beginTime = Date.now();
    var router = Current.router;
    var timerFunc = function() {
        setTimeout(function() {
            ctx ? cb.call(ctx) : cb();
        }, 1);
    };
    if (router === null) return timerFunc();
    var path = router.$taroPath;
    function next() {
        var _$_a, _b, _c;
        var pageElement = env.document.getElementById(path);
        if (pageElement === null || pageElement === void 0 ? void 0 : pageElement.pendingUpdate) {
            if (true) {
                (_c = (_b = (_$_a = pageElement.firstChild) === null || _$_a === void 0 ? void 0 : _$_a["componentOnReady"]) === null || _b === void 0 ? void 0 : _b.call(_$_a).then(function() {
                    timerFunc();
                })) !== null && _c !== void 0 ? _c : timerFunc();
            } else {
                pageElement.enqueueUpdateCallback(cb, ctx);
            }
        } else if (Date.now() - beginTime > TIMEOUT) {
            timerFunc();
        } else {
            setTimeout(function() {
                return next();
            }, 20);
        }
    }
    next();
};
function handleArrayFindPolyfill() {
    if (!isFunction(Array.prototype.find)) {
        Object.defineProperty(Array.prototype, "find", {
            value: function value(predicate) {
                if (this == null) {
                    throw new TypeError('"this" is null or not defined');
                }
                var o = Object(this);
                var len = o.length >>> 0;
                if (!isFunction(predicate)) {
                    throw new TypeError("predicate must be a function");
                }
                var thisArg = arguments[1];
                var k = 0;
                while(k < len){
                    var kValue = o[k];
                    if (predicate.call(thisArg, kValue, k, o)) {
                        return kValue;
                    }
                    k++;
                }
                return void 0;
            }
        });
    }
}
function handleArrayIncludesPolyfill() {
    if (!isFunction(Array.prototype.includes)) {
        Object.defineProperty(Array.prototype, "includes", {
            value: function value(searchElement, fromIndex) {
                if (this == null) {
                    throw new TypeError('"this" is null or not defined');
                }
                var o = Object(this);
                var len = o.length >>> 0;
                if (len === 0) {
                    return false;
                }
                var n = fromIndex | 0;
                var k = Math.max(n >= 0 ? n : len - Math.abs(n), 0);
                while(k < len){
                    if (o[k] === searchElement) {
                        return true;
                    }
                    k++;
                }
                return false;
            }
        });
    }
}
function handleIntersectionObserverPolyfill() {
    if ("IntersectionObserver" in window && "IntersectionObserverEntry" in window && "intersectionRatio" in window.IntersectionObserverEntry.prototype) {
        if (!("isIntersecting" in window.IntersectionObserverEntry.prototype)) {
            Object.defineProperty(window.IntersectionObserverEntry.prototype, "isIntersecting", {
                get: function get() {
                    return this.intersectionRatio > 0;
                }
            });
        }
    } else {
        handleIntersectionObserverObjectPolyfill();
    }
}
function handleIntersectionObserverObjectPolyfill() {
    var document2 = window.document;
    function IntersectionObserverEntry(entry) {
        this.time = entry.time;
        this.target = entry.target;
        this.rootBounds = entry.rootBounds;
        this.boundingClientRect = entry.boundingClientRect;
        this.intersectionRect = entry.intersectionRect || getEmptyRect();
        this.isIntersecting = !!entry.intersectionRect;
        var targetRect = this.boundingClientRect;
        var targetArea = targetRect.width * targetRect.height;
        var intersectionRect = this.intersectionRect;
        var intersectionArea = intersectionRect.width * intersectionRect.height;
        if (targetArea) {
            this.intersectionRatio = Number((intersectionArea / targetArea).toFixed(4));
        } else {
            this.intersectionRatio = this.isIntersecting ? 1 : 0;
        }
    }
    function IntersectionObserver(callback) {
        var options2 = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
        if (!isFunction(callback)) {
            throw new Error("callback must be a function");
        }
        if (options2.root && options2.root.nodeType != 1) {
            throw new Error("root must be an Element");
        }
        this._checkForIntersections = throttle(this._checkForIntersections.bind(this), this.THROTTLE_TIMEOUT);
        this._callback = callback;
        this._observationTargets = [];
        this._queuedEntries = [];
        this._rootMarginValues = this._parseRootMargin(options2.rootMargin);
        this.thresholds = this._initThresholds(options2.threshold);
        this.root = options2.root || null;
        this.rootMargin = this._rootMarginValues.map(function(margin) {
            return margin.value + margin.unit;
        }).join(" ");
    }
    IntersectionObserver.prototype.THROTTLE_TIMEOUT = 100;
    IntersectionObserver.prototype.POLL_INTERVAL = null;
    IntersectionObserver.prototype.USE_MUTATION_OBSERVER = true;
    IntersectionObserver.prototype.observe = function(target) {
        var isTargetAlreadyObserved = this._observationTargets.some(function(item) {
            return item.element == target;
        });
        if (isTargetAlreadyObserved) return;
        if (!(target && target.nodeType == 1)) {
            throw new Error("target must be an Element");
        }
        this._registerInstance();
        this._observationTargets.push({
            element: target,
            entry: null
        });
        this._monitorIntersections();
        this._checkForIntersections();
    };
    IntersectionObserver.prototype.unobserve = function(target) {
        this._observationTargets = this._observationTargets.filter(function(item) {
            return item.element != target;
        });
        if (!this._observationTargets.length) {
            this._unmonitorIntersections();
            this._unregisterInstance();
        }
    };
    IntersectionObserver.prototype.disconnect = function() {
        this._observationTargets = [];
        this._unmonitorIntersections();
        this._unregisterInstance();
    };
    IntersectionObserver.prototype.takeRecords = function() {
        var records = this._queuedEntries.slice();
        this._queuedEntries = [];
        return records;
    };
    IntersectionObserver.prototype._initThresholds = function(opt_threshold) {
        var threshold = opt_threshold || [
            0
        ];
        if (!Array.isArray(threshold)) threshold = [
            threshold
        ];
        return threshold.sort().filter(function(t, i, a) {
            if (!isNumber(t) || isNaN(t) || t < 0 || t > 1) {
                throw new Error("threshold must be a number between 0 and 1 inclusively");
            }
            return t !== a[i - 1];
        });
    };
    IntersectionObserver.prototype._parseRootMargin = function(opt_rootMargin) {
        var marginString = opt_rootMargin || "0px";
        var margins = marginString.split(/\s+/).map(function(margin) {
            var parts = /^(-?\d*\.?\d+)(px|%)$/.exec(margin);
            if (!parts) {
                throw new Error("rootMargin must be specified in pixels or percent");
            }
            return {
                value: parseFloat(parts[1]),
                unit: parts[2]
            };
        });
        margins[1] = margins[1] || margins[0];
        margins[2] = margins[2] || margins[0];
        margins[3] = margins[3] || margins[1];
        return margins;
    };
    IntersectionObserver.prototype._monitorIntersections = function() {
        if (!this._monitoringIntersections) {
            this._monitoringIntersections = true;
            if (this.POLL_INTERVAL) {
                this._monitoringInterval = setInterval(this._checkForIntersections, this.POLL_INTERVAL);
            } else {
                addEvent(window, "resize", this._checkForIntersections, true);
                addEvent(document2, "scroll", this._checkForIntersections, true);
                if (this.USE_MUTATION_OBSERVER && "MutationObserver" in window) {
                    this._domObserver = new MutationObserver(this._checkForIntersections);
                    this._domObserver.observe(document2, {
                        attributes: true,
                        childList: true,
                        characterData: true,
                        subtree: true
                    });
                }
            }
        }
    };
    IntersectionObserver.prototype._unmonitorIntersections = function() {
        if (this._monitoringIntersections) {
            this._monitoringIntersections = false;
            clearInterval(this._monitoringInterval);
            this._monitoringInterval = null;
            removeEvent(window, "resize", this._checkForIntersections, true);
            removeEvent(document2, "scroll", this._checkForIntersections, true);
            if (this._domObserver) {
                this._domObserver.disconnect();
                this._domObserver = null;
            }
        }
    };
    IntersectionObserver.prototype._checkForIntersections = function() {
        var rootIsInDom = this._rootIsInDom();
        var rootRect = rootIsInDom ? this._getRootRect() : getEmptyRect();
        this._observationTargets.forEach(function(item) {
            var target = item.element;
            var targetRect = getBoundingClientRect(target);
            var rootContainsTarget = this._rootContainsTarget(target);
            var oldEntry = item.entry;
            var intersectionRect = rootIsInDom && rootContainsTarget && this._computeTargetAndRootIntersection(target, rootRect);
            var newEntry = item.entry = new IntersectionObserverEntry({
                time: now2(),
                target: target,
                boundingClientRect: targetRect,
                rootBounds: rootRect,
                intersectionRect: intersectionRect,
                intersectionRatio: -1,
                isIntersecting: false
            });
            if (!oldEntry) {
                this._queuedEntries.push(newEntry);
            } else if (rootIsInDom && rootContainsTarget) {
                if (this._hasCrossedThreshold(oldEntry, newEntry)) {
                    this._queuedEntries.push(newEntry);
                }
            } else {
                if (oldEntry && oldEntry.isIntersecting) {
                    this._queuedEntries.push(newEntry);
                }
            }
        }, this);
        if (this._queuedEntries.length) {
            this._callback(this.takeRecords(), this);
        }
    };
    IntersectionObserver.prototype._computeTargetAndRootIntersection = function(target, rootRect) {
        if (window.getComputedStyle(target).display === "none") return;
        var targetRect = getBoundingClientRect(target);
        var intersectionRect = targetRect;
        var parent = getParentNode(target);
        var atRoot = false;
        while(!atRoot){
            var parentRect = null;
            var parentComputedStyle = parent.nodeType == 1 ? window.getComputedStyle(parent) : {};
            if (parentComputedStyle.display === "none") return;
            if (parent == this.root || parent == document2) {
                atRoot = true;
                parentRect = rootRect;
            } else {
                if (parent != document2.body && parent != document2.documentElement && parentComputedStyle.overflow != "visible") {
                    parentRect = getBoundingClientRect(parent);
                }
            }
            if (parentRect) {
                intersectionRect = computeRectIntersection(parentRect, intersectionRect);
                if (!intersectionRect) break;
            }
            parent = getParentNode(parent);
        }
        return intersectionRect;
    };
    IntersectionObserver.prototype._getRootRect = function() {
        var rootRect;
        if (this.root) {
            rootRect = getBoundingClientRect(this.root);
        } else {
            var html = document2.documentElement;
            var body = document2.body;
            rootRect = {
                top: 0,
                left: 0,
                right: html.clientWidth || body.clientWidth,
                width: html.clientWidth || body.clientWidth,
                bottom: html.clientHeight || body.clientHeight,
                height: html.clientHeight || body.clientHeight
            };
        }
        return this._expandRectByRootMargin(rootRect);
    };
    IntersectionObserver.prototype._expandRectByRootMargin = function(rect) {
        var margins = this._rootMarginValues.map(function(margin, i) {
            return margin.unit === "px" ? margin.value : margin.value * (i % 2 ? rect.width : rect.height) / 100;
        });
        var newRect = {
            top: rect.top - margins[0],
            right: rect.right + margins[1],
            bottom: rect.bottom + margins[2],
            left: rect.left - margins[3]
        };
        newRect.width = newRect.right - newRect.left;
        newRect.height = newRect.bottom - newRect.top;
        return newRect;
    };
    IntersectionObserver.prototype._hasCrossedThreshold = function(oldEntry, newEntry) {
        var oldRatio = oldEntry && oldEntry.isIntersecting ? oldEntry.intersectionRatio || 0 : -1;
        var newRatio = newEntry.isIntersecting ? newEntry.intersectionRatio || 0 : -1;
        if (oldRatio === newRatio) return;
        for(var i = 0; i < this.thresholds.length; i++){
            var threshold = this.thresholds[i];
            if (threshold == oldRatio || threshold == newRatio || threshold < oldRatio !== threshold < newRatio) {
                return true;
            }
        }
    };
    IntersectionObserver.prototype._rootIsInDom = function() {
        return !this.root || containsDeep(document2, this.root);
    };
    IntersectionObserver.prototype._rootContainsTarget = function(target) {
        return containsDeep(this.root || document2, target);
    };
    IntersectionObserver.prototype._registerInstance = function() {};
    IntersectionObserver.prototype._unregisterInstance = function() {};
    function now2() {
        return window.performance && performance.now && performance.now();
    }
    function addEvent(node, event, fn, opt_useCapture) {
        if (isFunction(node.addEventListener)) {
            node.addEventListener(event, fn, opt_useCapture || false);
        } else if (isFunction(node.attachEvent)) {
            node.attachEvent("on" + event, fn);
        }
    }
    function removeEvent(node, event, fn, opt_useCapture) {
        if (isFunction(node.removeEventListener)) {
            node.removeEventListener(event, fn, opt_useCapture || false);
        } else if (isFunction(node.detatchEvent)) {
            node.detatchEvent("on" + event, fn);
        }
    }
    function computeRectIntersection(rect1, rect2) {
        var top = Math.max(rect1.top, rect2.top);
        var bottom = Math.min(rect1.bottom, rect2.bottom);
        var left = Math.max(rect1.left, rect2.left);
        var right = Math.min(rect1.right, rect2.right);
        var width2 = right - left;
        var height = bottom - top;
        return width2 >= 0 && height >= 0 && {
            top: top,
            bottom: bottom,
            left: left,
            right: right,
            width: width2,
            height: height
        };
    }
    function getBoundingClientRect(el) {
        var rect;
        try {
            rect = el.getBoundingClientRect();
        } catch (err) {}
        if (!rect) return getEmptyRect();
        if (!(rect.width && rect.height)) {
            rect = {
                top: rect.top,
                right: rect.right,
                bottom: rect.bottom,
                left: rect.left,
                width: rect.right - rect.left,
                height: rect.bottom - rect.top
            };
        }
        return rect;
    }
    function getEmptyRect() {
        return {
            top: 0,
            bottom: 0,
            left: 0,
            right: 0,
            width: 0,
            height: 0
        };
    }
    function containsDeep(parent, child) {
        var node = child;
        while(node){
            if (node == parent) return true;
            node = getParentNode(node);
        }
        return false;
    }
    function getParentNode(node) {
        var parent = node.parentNode;
        if (parent && parent.nodeType == 11 && parent.host) {
            return parent.host;
        }
        if (parent && parent.assignedSlot) {
            return parent.assignedSlot.parentNode;
        }
        return parent;
    }
    window.IntersectionObserver = IntersectionObserver;
    window.IntersectionObserverEntry = IntersectionObserverEntry;
}
function handleObjectAssignPolyfill() {
    if (!isFunction(Object.assign)) {
        Object.assign = function(target) {
            if (target == null) {
                throw new TypeError("Cannot convert undefined or null to object");
            }
            var to = Object(target);
            for(var index = 1; index < arguments.length; index++){
                var nextSource = arguments[index];
                if (nextSource != null) {
                    for(var nextKey in nextSource){
                        if (Object.prototype.hasOwnProperty.call(nextSource, nextKey)) {
                            to[nextKey] = nextSource[nextKey];
                        }
                    }
                }
            }
            return to;
        };
    }
}
function handleObjectEntriesPolyfill() {
    if (!isFunction(Object.entries)) {
        Object.entries = function(obj) {
            if (obj == null) {
                throw new TypeError("Cannot convert undefined or null to object");
            }
            var to = [];
            if (obj != null) {
                for(var key in obj){
                    if (Object.prototype.hasOwnProperty.call(obj, key)) {
                        to.push([
                            key,
                            obj[key]
                        ]);
                    }
                }
            }
            return to;
        };
    }
}
function handleObjectDefinePropertyPolyfill() {
    if (!isFunction(Object.defineProperties)) {
        Object.defineProperties = function(obj, properties) {
            function convertToDescriptor(desc) {
                function hasProperty(obj2, prop) {
                    return Object.prototype.hasOwnProperty.call(obj2, prop);
                }
                if (!isObject(desc)) {
                    throw new TypeError("bad desc");
                }
                var d = {};
                if (hasProperty(desc, "enumerable")) d.enumerable = !!desc.enumerable;
                if (hasProperty(desc, "configurable")) {
                    d.configurable = !!desc.configurable;
                }
                if (hasProperty(desc, "value")) d.value = desc.value;
                if (hasProperty(desc, "writable")) d.writable = !!desc.writable;
                if (hasProperty(desc, "get")) {
                    var g = desc.get;
                    if (!isFunction(g) && !isUndefined(g)) {
                        throw new TypeError("bad get");
                    }
                    d.get = g;
                }
                if (hasProperty(desc, "set")) {
                    var s = desc.set;
                    if (!isFunction(s) && !isUndefined(s)) {
                        throw new TypeError("bad set");
                    }
                    d.set = s;
                }
                if (("get" in d || "set" in d) && ("value" in d || "writable" in d)) {
                    throw new TypeError("identity-confused descriptor");
                }
                return d;
            }
            if (!isObject(obj)) throw new TypeError("bad obj");
            properties = Object(properties);
            var keys = Object.keys(properties);
            var descs = [];
            for(var i = 0; i < keys.length; i++){
                descs.push([
                    keys[i],
                    convertToDescriptor(properties[keys[i]])
                ]);
            }
            for(var i1 = 0; i1 < descs.length; i1++){
                Object.defineProperty(obj, descs[i1][0], descs[i1][1]);
            }
            return obj;
        };
    }
}
function handlePolyfill() {
    if (true) {
        handleObjectAssignPolyfill();
    }
    if (true) {
        handleObjectEntriesPolyfill();
    }
    if (true) {
        handleObjectDefinePropertyPolyfill();
    }
    if (true) {
        handleArrayFindPolyfill();
    }
    if (true) {
        handleArrayIncludesPolyfill();
    }
    if (isObject(window)) {
        if (true) {
            handleIntersectionObserverPolyfill();
        }
    }
}
if (false) {
    handlePolyfill();
}
export { __rest, __awaiter, PROPERTY_THRESHOLD, TARO_RUNTIME, HOOKS_APP_ID, SET_DATA, PAGE_INIT, ROOT_STR, HTML, HEAD, BODY, APP, CONTAINER, DOCUMENT_ELEMENT_NAME, DOCUMENT_FRAGMENT, ID, UID, CLASS, STYLE, FOCUS, VIEW, STATIC_VIEW, PURE_VIEW, PROPS, DATASET, OBJECT, VALUE, INPUT, CHANGE, CUSTOM_WRAPPER, TARGET, CURRENT_TARGET, TYPE, CONFIRM, TIME_STAMP, KEY_CODE, TOUCHMOVE, DATE, SET_TIMEOUT, COMPILE_MODE, CATCHMOVE, CATCH_VIEW, COMMENT, ON_LOAD, ON_READY, ON_SHOW, ON_HIDE, OPTIONS, EXTERNAL_CLASSES, EVENT_CALLBACK_RESULT, BEHAVIORS, A, CONTEXT_ACTIONS, MutationObserver$1, incrementId, isHasExtractProp, eventSource, env, hydrate, TaroNode, Style, TaroElement, options, TaroEvent, createEvent, eventHandler, FormElement, TaroRootElement, TaroText, URLSearchParams, URL, parseUrl, document$1, getComputedStyle, eventCenter, History, Current, getCurrentInstance, Location, nav, now, _raf, _caf, window$1, location, history, SVGElement, addLeadingSlash, hasBasename, stripBasename, stripTrailing, stripSuffix, getHomePage, getCurrentPage, injectPageInstance, getPageInstance, removePageInstance, safeExecute, stringify, getPath, getOnReadyEventKey, getOnShowEventKey, getOnHideEventKey, createPageConfig, createComponentConfig, createRecursiveComponentConfig, nextTick, handlePolyfill };
