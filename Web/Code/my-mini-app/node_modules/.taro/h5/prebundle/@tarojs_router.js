import { createBrowserHistory, createHashHistory, createMpaHistory, createMultiRouter, createRouter, getCurrentPages, handleAppMount, handleAppMountWithTabbar, history, isDingTalk, isWeixin, navigateBack, navigateTo, prependBasename, reLaunch, redirectTo, routesAlias, setHistory, setHistoryMode, setMpaTitle, setNavigationBarLoading, setNavigationBarStyle, setTitle, switchTab } from "./chunk-Y34APMNO.js";
import "./chunk-LYLNOLBG.js";
import "./chunk-KTDWUN4J.js";
import "./chunk-VRMIYLYE.js";
import "./chunk-UG6XUGBP.js";
export { createBrowserHistory, createHashHistory, createMpaHistory, createMultiRouter, createRouter, getCurrentPages, handleAppMount, handleAppMountWithTabbar, history, isDingTalk, isWeixin, navigateBack, navigateTo, prependBasename, reLaunch, redirectTo, routes<PERSON>lias, setHistory, setHistoryMode, setMpaTitle, setNavigationBarLoading, setNavigationBarStyle, setTitle, switchTab };
