import { A, APP, BEHAVIORS, BODY, CATCHMOVE, CATCH_VIEW, CHAN<PERSON>, CLA<PERSON>, COMMENT, COMPILE_MODE, CONFIRM, CONTAINER, CONTEXT_ACTIONS, CURRENT_TARGET, CUSTOM_WRAPPER, Current, DATASET, DATE, DOCUMENT_ELEMENT_NAME, DOCUMENT_FRAGMENT, EVENT_CALLBACK_RESULT, EXTERNAL_CLASSES, FOCUS, FormElement, HEAD, HOOKS_APP_ID, HTML, History, ID, INPUT, KEY_CODE, Location, MutationObserver$1, OBJECT, ON_HIDE, ON_LOAD, ON_READY, ON_SHOW, OPTIONS, PAGE_INIT, PROPERTY_THRESHOLD, PROPS, PURE_VIEW, ROOT_STR, SET_DATA, SET_TIMEOUT, STATIC_VIEW, STYLE, SVGElement, Style, TARGET, TARO_RUNTIME, TIME_STAMP, <PERSON>UC<PERSON><PERSON><PERSON>, <PERSON>YP<PERSON>, TaroElement, TaroEvent, TaroNode, TaroRootElement, TaroText, UID, URL, URLSearchParams, VALUE, VIEW, _caf, _raf, addLeadingSlash, createComponentConfig, createEvent, createPageConfig, createRecursiveComponentConfig, document$1, env, eventCenter, eventHandler, eventSource, getComputedStyle, getCurrentInstance, getCurrentPage, getHomePage, getOnHideEventKey, getOnReadyEventKey, getOnShowEventKey, getPageInstance, getPath, handlePolyfill, hasBasename, history, hydrate, incrementId, injectPageInstance, isHasExtractProp, location, nav, nextTick, now, options, parseUrl, removePageInstance, safeExecute, stringify, stripBasename, stripSuffix, stripTrailing, window$1 } from "./chunk-KTDWUN4J.js";
import { Events, hooks } from "./chunk-VRMIYLYE.js";
import "./chunk-UG6XUGBP.js";
export { A, APP, BEHAVIORS, BODY, CATCHMOVE, CATCH_VIEW, CHANGE, CLASS, COMMENT, COMPILE_MODE, CONFIRM, CONTAINER, CONTEXT_ACTIONS, CURRENT_TARGET, CUSTOM_WRAPPER, Current, DATASET, DATE, DOCUMENT_ELEMENT_NAME, DOCUMENT_FRAGMENT, EVENT_CALLBACK_RESULT, EXTERNAL_CLASSES, Events, FOCUS, FormElement, HEAD, HOOKS_APP_ID, HTML, History, ID, INPUT, KEY_CODE, Location, MutationObserver$1 as MutationObserver, OBJECT, ON_HIDE, ON_LOAD, ON_READY, ON_SHOW, OPTIONS, PAGE_INIT, PROPERTY_THRESHOLD, PROPS, PURE_VIEW, ROOT_STR, SET_DATA, SET_TIMEOUT, STATIC_VIEW, STYLE, SVGElement, Style, TARGET, TARO_RUNTIME, TIME_STAMP, TOUCHMOVE, TYPE, TaroElement, TaroEvent, TaroNode, TaroRootElement, TaroText, UID, URL, URLSearchParams, VALUE, VIEW, addLeadingSlash, _caf as cancelAnimationFrame, createComponentConfig, createEvent, createPageConfig, createRecursiveComponentConfig, document$1 as document, env, eventCenter, eventHandler, eventSource, getComputedStyle, getCurrentInstance, getCurrentPage, getHomePage, getOnHideEventKey, getOnReadyEventKey, getOnShowEventKey, getPageInstance, getPath, handlePolyfill, hasBasename, history, hooks, hydrate, incrementId, injectPageInstance, isHasExtractProp, location, nav as navigator, nextTick, now, options, parseUrl, removePageInstance, _raf as requestAnimationFrame, safeExecute, stringify, stripBasename, stripSuffix, stripTrailing, window$1 as window };
