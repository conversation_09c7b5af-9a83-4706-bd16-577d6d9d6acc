function _array_like_to_array(arr, len) {
    if (len == null || len > arr.length) len = arr.length;
    for(var i = 0, arr2 = new Array(len); i < len; i++)arr2[i] = arr[i];
    return arr2;
}
function _array_without_holes(arr) {
    if (Array.isArray(arr)) return _array_like_to_array(arr);
}
function _iterable_to_array(iter) {
    if (typeof Symbol !== "undefined" && iter[Symbol.iterator] != null || iter["@@iterator"] != null) return Array.from(iter);
}
function _non_iterable_spread() {
    throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}
function _to_consumable_array(arr) {
    return _array_without_holes(arr) || _iterable_to_array(arr) || _unsupported_iterable_to_array(arr) || _non_iterable_spread();
}
function _unsupported_iterable_to_array(o, minLen) {
    if (!o) return;
    if (typeof o === "string") return _array_like_to_array(o, minLen);
    var n = Object.prototype.toString.call(o).slice(8, -1);
    if (n === "Object" && o.constructor) n = o.constructor.name;
    if (n === "Map" || n === "Set") return Array.from(n);
    if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _array_like_to_array(o, minLen);
}
import "./chunk-UG6XUGBP.js";
// node_modules/@stencil/core/internal/client/shadow-css.js
var safeSelector = function(selector) {
    var placeholders = [];
    var index = 0;
    selector = selector.replace(/(\[[^\]]*\])/g, function(_, keep) {
        var replaceBy = "__ph-".concat(index, "__");
        placeholders.push(keep);
        index++;
        return replaceBy;
    });
    var content = selector.replace(/(:nth-[-\w]+)(\([^)]+\))/g, function(_, pseudo, exp) {
        var replaceBy = "__ph-".concat(index, "__");
        placeholders.push(exp);
        index++;
        return pseudo + replaceBy;
    });
    var ss = {
        content: content,
        placeholders: placeholders
    };
    return ss;
};
var restoreSafeSelector = function(placeholders, content) {
    return content.replace(/__ph-(\d+)__/g, function(_, index) {
        return placeholders[+index];
    });
};
var _polyfillHost = "-shadowcsshost";
var _polyfillSlotted = "-shadowcssslotted";
var _polyfillHostContext = "-shadowcsscontext";
var _parenSuffix = ")(?:\\(((?:\\([^)(]*\\)|[^)(]*)+?)\\))?([^,{]*)";
var _cssColonHostRe = new RegExp("(" + _polyfillHost + _parenSuffix, "gim");
var _cssColonHostContextRe = new RegExp("(" + _polyfillHostContext + _parenSuffix, "gim");
var _cssColonSlottedRe = new RegExp("(" + _polyfillSlotted + _parenSuffix, "gim");
var _polyfillHostNoCombinator = _polyfillHost + "-no-combinator";
var _polyfillHostNoCombinatorRe = /-shadowcsshost-no-combinator([^\s]*)/;
var _shadowDOMSelectorsRe = [
    /::shadow/g,
    /::content/g
];
var _selectorReSuffix = "([>\\s~+[.,{:][\\s\\S]*)?$";
var _polyfillHostRe = /-shadowcsshost/gim;
var _colonHostRe = /:host/gim;
var _colonSlottedRe = /::slotted/gim;
var _colonHostContextRe = /:host-context/gim;
var _commentRe = /\/\*\s*[\s\S]*?\*\//g;
var stripComments = function(input) {
    return input.replace(_commentRe, "");
};
var _commentWithHashRe = /\/\*\s*#\s*source(Mapping)?URL=[\s\S]+?\*\//g;
var extractCommentsWithHash = function(input) {
    return input.match(_commentWithHashRe) || [];
};
var _ruleRe = /(\s*)([^;\{\}]+?)(\s*)((?:{%BLOCK%}?\s*;?)|(?:\s*;))/g;
var _curlyRe = /([{}])/g;
var _selectorPartsRe = /(^.*?[^\\])??((:+)(.*)|$)/;
var OPEN_CURLY = "{";
var CLOSE_CURLY = "}";
var BLOCK_PLACEHOLDER = "%BLOCK%";
var processRules = function(input, ruleCallback) {
    var inputWithEscapedBlocks = escapeBlocks(input);
    var nextBlockIndex = 0;
    return inputWithEscapedBlocks.escapedString.replace(_ruleRe, function() {
        for(var _len = arguments.length, m = new Array(_len), _key = 0; _key < _len; _key++){
            m[_key] = arguments[_key];
        }
        var selector = m[2];
        var content = "";
        var suffix = m[4];
        var contentPrefix = "";
        if (suffix && suffix.startsWith("{" + BLOCK_PLACEHOLDER)) {
            content = inputWithEscapedBlocks.blocks[nextBlockIndex++];
            suffix = suffix.substring(BLOCK_PLACEHOLDER.length + 1);
            contentPrefix = "{";
        }
        var cssRule = {
            selector: selector,
            content: content
        };
        var rule = ruleCallback(cssRule);
        return "".concat(m[1]).concat(rule.selector).concat(m[3]).concat(contentPrefix).concat(rule.content).concat(suffix);
    });
};
var escapeBlocks = function(input) {
    var inputParts = input.split(_curlyRe);
    var resultParts = [];
    var escapedBlocks = [];
    var bracketCount = 0;
    var currentBlockParts = [];
    for(var partIndex = 0; partIndex < inputParts.length; partIndex++){
        var part = inputParts[partIndex];
        if (part === CLOSE_CURLY) {
            bracketCount--;
        }
        if (bracketCount > 0) {
            currentBlockParts.push(part);
        } else {
            if (currentBlockParts.length > 0) {
                escapedBlocks.push(currentBlockParts.join(""));
                resultParts.push(BLOCK_PLACEHOLDER);
                currentBlockParts = [];
            }
            resultParts.push(part);
        }
        if (part === OPEN_CURLY) {
            bracketCount++;
        }
    }
    if (currentBlockParts.length > 0) {
        escapedBlocks.push(currentBlockParts.join(""));
        resultParts.push(BLOCK_PLACEHOLDER);
    }
    var strEscapedBlocks = {
        escapedString: resultParts.join(""),
        blocks: escapedBlocks
    };
    return strEscapedBlocks;
};
var insertPolyfillHostInCssText = function(selector) {
    selector = selector.replace(_colonHostContextRe, _polyfillHostContext).replace(_colonHostRe, _polyfillHost).replace(_colonSlottedRe, _polyfillSlotted);
    return selector;
};
var convertColonRule = function(cssText, regExp, partReplacer) {
    return cssText.replace(regExp, function() {
        for(var _len = arguments.length, m = new Array(_len), _key = 0; _key < _len; _key++){
            m[_key] = arguments[_key];
        }
        if (m[2]) {
            var parts = m[2].split(",");
            var r = [];
            for(var i = 0; i < parts.length; i++){
                var p = parts[i].trim();
                if (!p) break;
                r.push(partReplacer(_polyfillHostNoCombinator, p, m[3]));
            }
            return r.join(",");
        } else {
            return _polyfillHostNoCombinator + m[3];
        }
    });
};
var colonHostPartReplacer = function(host, part, suffix) {
    return host + part.replace(_polyfillHost, "") + suffix;
};
var convertColonHost = function(cssText) {
    return convertColonRule(cssText, _cssColonHostRe, colonHostPartReplacer);
};
var colonHostContextPartReplacer = function(host, part, suffix) {
    if (part.indexOf(_polyfillHost) > -1) {
        return colonHostPartReplacer(host, part, suffix);
    } else {
        return host + part + suffix + ", " + part + " " + host + suffix;
    }
};
var convertColonSlotted = function(cssText, slotScopeId) {
    var slotClass = "." + slotScopeId + " > ";
    var selectors = [];
    cssText = cssText.replace(_cssColonSlottedRe, function() {
        for(var _len = arguments.length, m = new Array(_len), _key = 0; _key < _len; _key++){
            m[_key] = arguments[_key];
        }
        if (m[2]) {
            var compound = m[2].trim();
            var suffix = m[3];
            var slottedSelector = slotClass + compound + suffix;
            var prefixSelector = "";
            for(var i = m[4] - 1; i >= 0; i--){
                var char = m[5][i];
                if (char === "}" || char === ",") {
                    break;
                }
                prefixSelector = char + prefixSelector;
            }
            var orgSelector = prefixSelector + slottedSelector;
            var addedSelector = "".concat(prefixSelector.trimRight()).concat(slottedSelector.trim());
            if (orgSelector.trim() !== addedSelector.trim()) {
                var updatedSelector = "".concat(addedSelector, ", ").concat(orgSelector);
                selectors.push({
                    orgSelector: orgSelector,
                    updatedSelector: updatedSelector
                });
            }
            return slottedSelector;
        } else {
            return _polyfillHostNoCombinator + m[3];
        }
    });
    return {
        selectors: selectors,
        cssText: cssText
    };
};
var convertColonHostContext = function(cssText) {
    return convertColonRule(cssText, _cssColonHostContextRe, colonHostContextPartReplacer);
};
var convertShadowDOMSelectors = function(cssText) {
    return _shadowDOMSelectorsRe.reduce(function(result, pattern) {
        return result.replace(pattern, " ");
    }, cssText);
};
var makeScopeMatcher = function(scopeSelector2) {
    var lre = /\[/g;
    var rre = /\]/g;
    scopeSelector2 = scopeSelector2.replace(lre, "\\[").replace(rre, "\\]");
    return new RegExp("^(" + scopeSelector2 + ")" + _selectorReSuffix, "m");
};
var selectorNeedsScoping = function(selector, scopeSelector2) {
    var re = makeScopeMatcher(scopeSelector2);
    return !re.test(selector);
};
var injectScopingSelector = function(selector, scopingSelector) {
    return selector.replace(_selectorPartsRe, function(_) {
        var before = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : "", _colonGroup = arguments.length > 2 ? arguments[2] : void 0, colon = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : "", after = arguments.length > 4 && arguments[4] !== void 0 ? arguments[4] : "";
        return before + scopingSelector + colon + after;
    });
};
var applySimpleSelectorScope = function(selector, scopeSelector2, hostSelector) {
    _polyfillHostRe.lastIndex = 0;
    if (_polyfillHostRe.test(selector)) {
        var replaceBy = ".".concat(hostSelector);
        return selector.replace(_polyfillHostNoCombinatorRe, function(_, selector2) {
            return injectScopingSelector(selector2, replaceBy);
        }).replace(_polyfillHostRe, replaceBy + " ");
    }
    return scopeSelector2 + " " + selector;
};
var applyStrictSelectorScope = function(selector, scopeSelector2, hostSelector) {
    var isRe = /\[is=([^\]]*)\]/g;
    scopeSelector2 = scopeSelector2.replace(isRe, function(_) {
        for(var _len = arguments.length, parts = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){
            parts[_key - 1] = arguments[_key];
        }
        return parts[0];
    });
    var className = "." + scopeSelector2;
    var _scopeSelectorPart = function(p) {
        var scopedP = p.trim();
        if (!scopedP) {
            return "";
        }
        if (p.indexOf(_polyfillHostNoCombinator) > -1) {
            scopedP = applySimpleSelectorScope(p, scopeSelector2, hostSelector);
        } else {
            var t = p.replace(_polyfillHostRe, "");
            if (t.length > 0) {
                scopedP = injectScopingSelector(t, className);
            }
        }
        return scopedP;
    };
    var safeContent = safeSelector(selector);
    selector = safeContent.content;
    var scopedSelector = "";
    var startIndex = 0;
    var res;
    var sep = /( |>|\+|~(?!=))\s*/g;
    var hasHost = selector.indexOf(_polyfillHostNoCombinator) > -1;
    var shouldScope = !hasHost;
    while((res = sep.exec(selector)) !== null){
        var separator = res[1];
        var part2 = selector.slice(startIndex, res.index).trim();
        shouldScope = shouldScope || part2.indexOf(_polyfillHostNoCombinator) > -1;
        var scopedPart = shouldScope ? _scopeSelectorPart(part2) : part2;
        scopedSelector += "".concat(scopedPart, " ").concat(separator, " ");
        startIndex = sep.lastIndex;
    }
    var part = selector.substring(startIndex);
    shouldScope = shouldScope || part.indexOf(_polyfillHostNoCombinator) > -1;
    scopedSelector += shouldScope ? _scopeSelectorPart(part) : part;
    return restoreSafeSelector(safeContent.placeholders, scopedSelector);
};
var scopeSelector = function(selector, scopeSelectorText, hostSelector, slotSelector) {
    return selector.split(",").map(function(shallowPart) {
        if (slotSelector && shallowPart.indexOf("." + slotSelector) > -1) {
            return shallowPart.trim();
        }
        if (selectorNeedsScoping(shallowPart, scopeSelectorText)) {
            return applyStrictSelectorScope(shallowPart, scopeSelectorText, hostSelector).trim();
        } else {
            return shallowPart.trim();
        }
    }).join(", ");
};
var scopeSelectors = function(cssText, scopeSelectorText, hostSelector, slotSelector, commentOriginalSelector) {
    return processRules(cssText, function(rule) {
        var selector = rule.selector;
        var content = rule.content;
        if (rule.selector[0] !== "@") {
            selector = scopeSelector(rule.selector, scopeSelectorText, hostSelector, slotSelector);
        } else if (rule.selector.startsWith("@media") || rule.selector.startsWith("@supports") || rule.selector.startsWith("@page") || rule.selector.startsWith("@document")) {
            content = scopeSelectors(rule.content, scopeSelectorText, hostSelector, slotSelector);
        }
        var cssRule = {
            selector: selector.replace(/\s{2,}/g, " ").trim(),
            content: content
        };
        return cssRule;
    });
};
var scopeCssText = function(cssText, scopeId, hostScopeId, slotScopeId, commentOriginalSelector) {
    cssText = insertPolyfillHostInCssText(cssText);
    cssText = convertColonHost(cssText);
    cssText = convertColonHostContext(cssText);
    var slotted = convertColonSlotted(cssText, slotScopeId);
    cssText = slotted.cssText;
    cssText = convertShadowDOMSelectors(cssText);
    if (scopeId) {
        cssText = scopeSelectors(cssText, scopeId, hostScopeId, slotScopeId);
    }
    cssText = cssText.replace(/-shadowcsshost-no-combinator/g, ".".concat(hostScopeId));
    cssText = cssText.replace(/>\s*\*\s+([^{, ]+)/gm, " $1 ");
    return {
        cssText: cssText.trim(),
        slottedSelectors: slotted.selectors
    };
};
var scopeCss = function(cssText, scopeId, commentOriginalSelector) {
    var hostScopeId = scopeId + "-h";
    var slotScopeId = scopeId + "-s";
    var commentsWithHash = extractCommentsWithHash(cssText);
    cssText = stripComments(cssText);
    var orgSelectors = [];
    if (commentOriginalSelector) {
        var processCommentedSelector = function(rule) {
            var placeholder = "/*!@___".concat(orgSelectors.length, "___*/");
            var comment = "/*!@".concat(rule.selector, "*/");
            orgSelectors.push({
                placeholder: placeholder,
                comment: comment
            });
            rule.selector = placeholder + rule.selector;
            return rule;
        };
        cssText = processRules(cssText, function(rule) {
            if (rule.selector[0] !== "@") {
                return processCommentedSelector(rule);
            } else if (rule.selector.startsWith("@media") || rule.selector.startsWith("@supports") || rule.selector.startsWith("@page") || rule.selector.startsWith("@document")) {
                rule.content = processRules(rule.content, processCommentedSelector);
                return rule;
            }
            return rule;
        });
    }
    var scoped = scopeCssText(cssText, scopeId, hostScopeId, slotScopeId);
    cssText = [
        scoped.cssText
    ].concat(_to_consumable_array(commentsWithHash)).join("\n");
    if (commentOriginalSelector) {
        orgSelectors.forEach(function(param) {
            var placeholder = param.placeholder, comment = param.comment;
            cssText = cssText.replace(placeholder, comment);
        });
    }
    scoped.slottedSelectors.forEach(function(slottedSelector) {
        cssText = cssText.replace(slottedSelector.orgSelector, slottedSelector.updatedSelector);
    });
    return cssText;
};
export { scopeCss }; /*! Bundled license information:

@stencil/core/internal/client/shadow-css.js:
  (**
   * @license
   * Copyright Google Inc. All Rights Reserved.
   *
   * Use of this source code is governed by an MIT-style license that can be
   * found in the LICENSE file at https://angular.io/license
   *
   * This file is a port of shadowCSS from webcomponents.js to TypeScript.
   * https://github.com/webcomponents/webcomponentsjs/blob/4efecd7e0e/src/ShadowCSS/ShadowCSS.js
   * https://github.com/angular/angular/blob/master/packages/compiler/src/shadow_css.ts
   *)
*/ 
