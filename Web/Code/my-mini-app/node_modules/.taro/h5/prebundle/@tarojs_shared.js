import { EMPTY_ARR, EMPTY_OBJ, EventChannel, Events, HOOK_TYPE, PLATFORM_CONFIG_MAP, PLATFORM_TYPE, Shortcuts, TaroHook, TaroHooks, animation, box, cacheDataGet, cacheDataHas, cacheDataSet, capitalize, controlledComponent, ensure, focusComponents, getComponentsAlias, getPlatformType, getUniqueKey, hasOwn, hooks, indent, internalComponents, isArray, isBoolean, isBooleanStringLiteral, isFunction, isNull, isNumber, isObject, isString, isUndefined, isWebPlatform, mergeInternalComponents, mergeReconciler, nestElements, nonsupport, noop, processApis, queryToJson, setUniqueKeyToRoute, singleQuote, toCamelCase, toDashed, toKebabCase, touchEvents, unbox, voidElements, warn } from "./chunk-VRMIYLYE.js";
import "./chunk-UG6XUGBP.js";
export { EMPTY_ARR, EMPTY_OBJ, EventChannel, Events, HOOK_TYPE, PLATFORM_CONFIG_MAP, PLATFORM_TYPE, Shortcuts, TaroHook, TaroHooks, animation, box, cacheDataGet, cacheDataHas, cacheDataSet, capitalize, controlledComponent, ensure, focusComponents, getComponentsAlias, getPlatformType, getUniqueKey, hasOwn, hooks, indent, internalComponents, isArray, isBoolean, isBooleanStringLiteral, isFunction, isNull, isNumber, isObject, isString, isUndefined, isWebPlatform, mergeInternalComponents, mergeReconciler, nestElements, nonsupport, noop, processApis, queryToJson, setUniqueKeyToRoute, singleQuote, toCamelCase, toDashed, toKebabCase, touchEvents, unbox, voidElements, warn };
