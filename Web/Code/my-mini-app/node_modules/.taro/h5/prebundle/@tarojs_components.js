function _assert_this_initialized(self) {
    if (self === void 0) {
        throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
    }
    return self;
}
function _class_call_check(instance, Constructor) {
    if (!(instance instanceof Constructor)) {
        throw new TypeError("Cannot call a class as a function");
    }
}
function _defineProperties(target, props) {
    for(var i = 0; i < props.length; i++){
        var descriptor = props[i];
        descriptor.enumerable = descriptor.enumerable || false;
        descriptor.configurable = true;
        if ("value" in descriptor) descriptor.writable = true;
        Object.defineProperty(target, descriptor.key, descriptor);
    }
}
function _create_class(Constructor, protoProps, staticProps) {
    if (protoProps) _defineProperties(Constructor.prototype, protoProps);
    if (staticProps) _defineProperties(Constructor, staticProps);
    return Constructor;
}
function _get_prototype_of(o) {
    _get_prototype_of = Object.setPrototypeOf ? Object.getPrototypeOf : function getPrototypeOf(o) {
        return o.__proto__ || Object.getPrototypeOf(o);
    };
    return _get_prototype_of(o);
}
function _inherits(subClass, superClass) {
    if (typeof superClass !== "function" && superClass !== null) {
        throw new TypeError("Super expression must either be null or a function");
    }
    subClass.prototype = Object.create(superClass && superClass.prototype, {
        constructor: {
            value: subClass,
            writable: true,
            configurable: true
        }
    });
    if (superClass) _set_prototype_of(subClass, superClass);
}
function _instanceof(left, right) {
    if (right != null && typeof Symbol !== "undefined" && right[Symbol.hasInstance]) {
        return !!right[Symbol.hasInstance](left);
    } else {
        return left instanceof right;
    }
}
function _possible_constructor_return(self, call) {
    if (call && (_type_of(call) === "object" || typeof call === "function")) {
        return call;
    }
    return _assert_this_initialized(self);
}
function _set_prototype_of(o, p) {
    _set_prototype_of = Object.setPrototypeOf || function setPrototypeOf(o, p) {
        o.__proto__ = p;
        return o;
    };
    return _set_prototype_of(o, p);
}
function _type_of(obj) {
    "@swc/helpers - typeof";
    return obj && typeof Symbol !== "undefined" && obj.constructor === Symbol ? "symbol" : typeof obj;
}
function _is_native_reflect_construct() {
    if (typeof Reflect === "undefined" || !Reflect.construct) return false;
    if (Reflect.construct.sham) return false;
    if (typeof Proxy === "function") return true;
    try {
        Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function() {}));
        return true;
    } catch (e) {
        return false;
    }
}
function _create_super(Derived) {
    var hasNativeReflectConstruct = _is_native_reflect_construct();
    return function _createSuperInternal() {
        var Super = _get_prototype_of(Derived), result;
        if (hasNativeReflectConstruct) {
            var NewTarget = _get_prototype_of(this).constructor;
            result = Reflect.construct(Super, arguments, NewTarget);
        } else {
            result = Super.apply(this, arguments);
        }
        return _possible_constructor_return(this, result);
    };
}
import { require_react_dom } from "./chunk-46SKMPFG.js";
import { require_react } from "./chunk-ZEZLQ4G4.js";
import { defineCustomElement, defineCustomElement10, defineCustomElement11, defineCustomElement12, defineCustomElement13, defineCustomElement14, defineCustomElement15, defineCustomElement16, defineCustomElement17, defineCustomElement18, defineCustomElement19, defineCustomElement2, defineCustomElement20, defineCustomElement21, defineCustomElement22, defineCustomElement23, defineCustomElement24, defineCustomElement25, defineCustomElement26, defineCustomElement27, defineCustomElement28, defineCustomElement29, defineCustomElement3, defineCustomElement30, defineCustomElement31, defineCustomElement32, defineCustomElement33, defineCustomElement34, defineCustomElement35, defineCustomElement36, defineCustomElement37, defineCustomElement38, defineCustomElement39, defineCustomElement4, defineCustomElement40, defineCustomElement41, defineCustomElement42, defineCustomElement43, defineCustomElement44, defineCustomElement45, defineCustomElement46, defineCustomElement47, defineCustomElement48, defineCustomElement49, defineCustomElement5, defineCustomElement50, defineCustomElement51, defineCustomElement52, defineCustomElement53, defineCustomElement54, defineCustomElement55, defineCustomElement56, defineCustomElement57, defineCustomElement58, defineCustomElement59, defineCustomElement6, defineCustomElement60, defineCustomElement61, defineCustomElement62, defineCustomElement63, defineCustomElement64, defineCustomElement65, defineCustomElement66, defineCustomElement67, defineCustomElement68, defineCustomElement69, defineCustomElement7, defineCustomElement70, defineCustomElement71, defineCustomElement72, defineCustomElement73, defineCustomElement74, defineCustomElement75, defineCustomElement76, defineCustomElement77, defineCustomElement78, defineCustomElement79, defineCustomElement8, defineCustomElement80, defineCustomElement81, defineCustomElement82, defineCustomElement83, defineCustomElement84, defineCustomElement85, defineCustomElement86, defineCustomElement87, defineCustomElement88, defineCustomElement89, defineCustomElement9 } from "./chunk-Y34APMNO.js";
import "./chunk-LYLNOLBG.js";
import { __rest } from "./chunk-KTDWUN4J.js";
import "./chunk-VRMIYLYE.js";
import { __toESM } from "./chunk-UG6XUGBP.js";
// node_modules/@tarojs/components/lib/react/components.js
var import_react4 = __toESM(require_react());
// node_modules/@tarojs/components/lib/react/helper.js
var manipulatePropsFunction = function(originalProps) {
    var propsToPass = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
    var dangerouslySetInnerHTML = originalProps.dangerouslySetInnerHTML, style = originalProps.style;
    if (typeof style !== "string") {
        propsToPass.style = style;
    }
    return Object.assign(Object.assign({}, propsToPass), {
        dangerouslySetInnerHTML: dangerouslySetInnerHTML
    });
};
// node_modules/@tarojs/components/lib/react/react-component-lib/createComponent.js
var import_react2 = __toESM(require_react());
// node_modules/@tarojs/components/lib/react/react-component-lib/utils/index.js
var import_react = __toESM(require_react());
// node_modules/@tarojs/components/lib/react/react-component-lib/utils/attachProps.js
var import_react_dom = __toESM(require_react_dom());
// node_modules/@tarojs/components/lib/react/react-component-lib/utils/case.js
var dashToPascalCase = function(str) {
    return str.toLowerCase().split("-").map(function(segment) {
        return segment.charAt(0).toUpperCase() + segment.slice(1);
    }).join("");
};
var camelToDashCase = function(str) {
    return str.replace(/([A-Z])/g, function(m) {
        return "-".concat(m[0].toLowerCase());
    });
};
// node_modules/@tarojs/components/lib/react/react-component-lib/utils/attachProps.js
var arrayToMap = function(arr) {
    var map = /* @__PURE__ */ new Map();
    arr.forEach(function(s) {
        return map.set(s, s);
    });
    return map;
};
var getClassName = function(classList, newProps, oldProps) {
    var newClassProp = newProps.className || newProps.class;
    var oldClassProp = oldProps.className || oldProps.class;
    var currentClasses = arrayToMap(classList);
    var incomingPropClasses = arrayToMap(newClassProp ? newClassProp.split(" ") : []);
    var oldPropClasses = arrayToMap(oldClassProp ? oldClassProp.split(" ") : []);
    var finalClassNames = [];
    currentClasses.forEach(function(currentClass) {
        if (incomingPropClasses.has(currentClass)) {
            finalClassNames.push(currentClass);
            incomingPropClasses.delete(currentClass);
        } else if (!oldPropClasses.has(currentClass)) {
            finalClassNames.push(currentClass);
        }
    });
    incomingPropClasses.forEach(function(s) {
        return finalClassNames.push(s);
    });
    return finalClassNames.join(" ");
};
var isCoveredByReact = function(__eventNameSuffix) {
    return false;
};
function getComponentName(node) {
    return node.tagName.replace(/^TARO-/, "").replace(/-CORE$/, "");
}
function getControlledValue(node) {
    var componentName = getComponentName(node);
    if ([
        "INPUT",
        "TEXTAREA",
        "SLIDER",
        "PICKER"
    ].includes(componentName)) {
        return "value";
    } else if (componentName === "SWITCH") {
        return "checked";
    } else {
        return null;
    }
}
function getPropsAfterReactUpdate(node) {
    var key = Object.keys(node).find(function(key2) {
        return key2.includes("__reactProps");
    });
    if (key) {
        return node[key];
    } else {
        return null;
    }
}
function finishedEventHandler(node) {
    var controlledValue = getControlledValue(node);
    if (!controlledValue) return;
    (0, import_react_dom.flushSync)(function() {});
    var newProps = getPropsAfterReactUpdate(node);
    if ((newProps === null || newProps === void 0 ? void 0 : newProps.hasOwnProperty(controlledValue)) && newProps[controlledValue] !== node[controlledValue]) {
        node[controlledValue] = newProps[controlledValue];
        node.setAttribute(controlledValue, newProps[controlledValue]);
    }
}
var syncEvent = function(node, eventName, newEventHandler) {
    var eventStore = node.__events || (node.__events = {});
    var oldEventHandler = eventStore[eventName];
    if (!newEventHandler) {
        oldEventHandler && node.removeEventListener(eventName, oldEventHandler);
    } else {
        if (oldEventHandler) {
            if (oldEventHandler.fn === newEventHandler) {
                return;
            } else {
                node.removeEventListener(eventName, oldEventHandler);
            }
        }
        var listener = eventStore[eventName] = function listener(e) {
            var _this = this;
            (0, import_react_dom.unstable_batchedUpdates)(function() {
                return newEventHandler.call(_this, e);
            });
            finishedEventHandler(node);
        };
        listener.fn = newEventHandler;
        node.addEventListener(eventName, listener);
    }
};
var attachProps = function(node, newProps) {
    var oldProps = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};
    if (_instanceof(node, Element)) {
        Object.keys(oldProps).forEach(function(name) {
            if ([
                "style",
                "children",
                "ref",
                "class",
                "className",
                "forwardedRef"
            ].includes(name)) {
                return;
            }
            if (!newProps.hasOwnProperty(name)) {
                if (/^on([A-Z].*)/.test(name)) {
                    var eventName = name.substring(2);
                    var eventNameLc = eventName.toLowerCase();
                    if (!isCoveredByReact(eventNameLc)) {
                        syncEvent(node, eventNameLc);
                    }
                } else {
                    node[name] = null;
                    node.removeAttribute(camelToDashCase(name));
                }
            }
        });
        node.className = getClassName(node.classList, newProps, oldProps);
        Object.keys(newProps).forEach(function(name) {
            if (name === "style" && typeof newProps[name] !== "string" || [
                "children",
                "ref",
                "class",
                "className",
                "forwardedRef"
            ].includes(name)) {
                return;
            }
            if (/^on([A-Z].*)/.test(name)) {
                var eventName = name.substring(2);
                var eventNameLc = eventName.toLowerCase();
                if (!isCoveredByReact(eventNameLc)) {
                    syncEvent(node, eventNameLc, newProps[name]);
                }
            } else {
                node[name] = newProps[name];
                var propType = _type_of(newProps[name]);
                if (propType === "string") {
                    node.setAttribute(camelToDashCase(name), newProps[name]);
                }
            }
        });
        var controlledValue = getControlledValue(node);
        if (controlledValue && newProps.hasOwnProperty(controlledValue)) {
            var handleChangeEvent = [
                "INPUT",
                "TEXTAREA"
            ].includes(getComponentName(node)) ? "input" : "change";
            node.__events || (node.__events = {});
            if (!node.__events.hasOwnProperty(handleChangeEvent)) {
                syncEvent(node, handleChangeEvent, function() {});
            }
        }
    }
};
function applyUnControlledDefaultValue(node, props) {
    var controlledValue = getControlledValue(node);
    if (!controlledValue) return;
    var defaultValueName = "default" + controlledValue.charAt(0).toUpperCase() + controlledValue.slice(1);
    if (!props.hasOwnProperty(controlledValue) && props.hasOwnProperty(defaultValueName)) {
        node[controlledValue] = props[defaultValueName];
        node.setAttribute(controlledValue, props[defaultValueName]);
    }
}
// node_modules/@tarojs/components/lib/react/react-component-lib/utils/index.js
var setRef = function(ref, value) {
    if (typeof ref === "function") {
        ref(value);
    } else if (ref != null) {
        ref.current = value;
    }
};
var mergeRefs = function() {
    for(var _len = arguments.length, refs = new Array(_len), _key = 0; _key < _len; _key++){
        refs[_key] = arguments[_key];
    }
    return function(value) {
        refs.forEach(function(ref) {
            setRef(ref, value);
        });
    };
};
var createForwardRef = function(ReactComponent, displayName) {
    var forwardRef = function(props, ref) {
        return import_react.default.createElement(ReactComponent, Object.assign({}, props, {
            forwardedRef: ref
        }));
    };
    forwardRef.displayName = displayName;
    return import_react.default.forwardRef(forwardRef);
};
// node_modules/@tarojs/components/lib/react/react-component-lib/createComponent.js
var createReactComponent = function(tagName, ReactComponentContext, manipulatePropsFunction2, defineCustomElement91) {
    if (defineCustomElement91 !== void 0) {
        defineCustomElement91();
    }
    var displayName = dashToPascalCase(tagName);
    var ReactComponent = /*#__PURE__*/ function(_import_react2_default_Component) {
        "use strict";
        _inherits(ReactComponent, _import_react2_default_Component);
        var _super = _create_super(ReactComponent);
        function ReactComponent(props) {
            _class_call_check(this, ReactComponent);
            var _this;
            _this = _super.call(this, props);
            _this.setComponentElRef = function(element) {
                _this.componentEl = element;
            };
            return _this;
        }
        _create_class(ReactComponent, [
            {
                key: "componentDidMount",
                value: function componentDidMount() {
                    applyUnControlledDefaultValue(this.componentEl, this.props);
                    this.componentDidUpdate(this.props);
                }
            },
            {
                key: "componentDidUpdate",
                value: function componentDidUpdate(prevProps) {
                    attachProps(this.componentEl, this.props, prevProps);
                }
            },
            {
                key: "render",
                value: function render() {
                    var _a = this.props, children = _a.children, forwardedRef = _a.forwardedRef, className = _a.className, ref = _a.ref, style = _a.style, cProps = __rest(_a, [
                        "children",
                        "forwardedRef",
                        "className",
                        "ref",
                        "style"
                    ]);
                    var propsToPass = Object.keys(cProps).reduce(function(acc, name) {
                        var value = cProps[name];
                        if (name.indexOf("on") === 0 && name[2] === name[2].toUpperCase()) {
                            var eventName = name.substring(2).toLowerCase();
                            if (typeof document !== "undefined" && isCoveredByReact(eventName)) {
                                acc[name] = value;
                            }
                        } else {
                            var type = typeof value === "undefined" ? "undefined" : _type_of(value);
                            if ([
                                "string",
                                "boolean",
                                "number"
                            ].includes(type)) {
                                acc[camelToDashCase(name)] = value;
                            }
                        }
                        return acc;
                    }, {});
                    if (manipulatePropsFunction2) {
                        propsToPass = manipulatePropsFunction2(this.props, propsToPass);
                    }
                    var newProps = Object.assign(Object.assign({}, propsToPass), {
                        ref: mergeRefs(forwardedRef, this.setComponentElRef)
                    });
                    return (0, import_react2.createElement)(tagName, newProps, children);
                }
            }
        ], [
            {
                key: "displayName",
                get: function get() {
                    return displayName;
                }
            }
        ]);
        return ReactComponent;
    }(import_react2.default.Component);
    if (ReactComponentContext) {
        ReactComponent.contextType = ReactComponentContext;
    }
    return createForwardRef(ReactComponent, displayName);
};
// node_modules/@tarojs/components/lib/react/react-component-lib/createOverlayComponent.js
var import_react3 = __toESM(require_react());
var import_react_dom2 = __toESM(require_react_dom());
// node_modules/@tarojs/components/lib/react/components.js
var Ad = createReactComponent("taro-ad-core", void 0, manipulatePropsFunction, defineCustomElement);
var AdCustom = createReactComponent("taro-ad-custom-core", void 0, manipulatePropsFunction, defineCustomElement2);
var AnimationVideo = createReactComponent("taro-animation-video-core", void 0, manipulatePropsFunction, defineCustomElement3);
var AnimationView = createReactComponent("taro-animation-view-core", void 0, manipulatePropsFunction, defineCustomElement4);
var ArCamera = createReactComponent("taro-ar-camera-core", void 0, manipulatePropsFunction, defineCustomElement5);
var Audio = createReactComponent("taro-audio-core", void 0, manipulatePropsFunction, defineCustomElement6);
var AwemeData = createReactComponent("taro-aweme-data-core", void 0, manipulatePropsFunction, defineCustomElement7);
var Block = import_react4.Fragment;
var Button = createReactComponent("taro-button-core", void 0, manipulatePropsFunction, defineCustomElement8);
var Camera = createReactComponent("taro-camera-core", void 0, manipulatePropsFunction, defineCustomElement9);
var Canvas = createReactComponent("taro-canvas-core", void 0, manipulatePropsFunction, defineCustomElement10);
var ChannelLive = createReactComponent("taro-channel-live-core", void 0, manipulatePropsFunction, defineCustomElement11);
var ChannelVideo = createReactComponent("taro-channel-video-core", void 0, manipulatePropsFunction, defineCustomElement12);
var Checkbox = createReactComponent("taro-checkbox-core", void 0, manipulatePropsFunction, defineCustomElement13);
var CheckboxGroup = createReactComponent("taro-checkbox-group-core", void 0, manipulatePropsFunction, defineCustomElement14);
var CommentDetail = createReactComponent("taro-comment-detail-core", void 0, manipulatePropsFunction, defineCustomElement15);
var CommentList = createReactComponent("taro-comment-list-core", void 0, manipulatePropsFunction, defineCustomElement16);
var ContactButton = createReactComponent("taro-contact-button-core", void 0, manipulatePropsFunction, defineCustomElement17);
var CoverImage = createReactComponent("taro-cover-image-core", void 0, manipulatePropsFunction, defineCustomElement18);
var CoverView = createReactComponent("taro-cover-view-core", void 0, manipulatePropsFunction, defineCustomElement19);
var CustomWrapper = createReactComponent("taro-custom-wrapper-core", void 0, manipulatePropsFunction, defineCustomElement20);
var DraggableSheet = createReactComponent("taro-draggable-sheet-core", void 0, manipulatePropsFunction, defineCustomElement21);
var Editor = createReactComponent("taro-editor-core", void 0, manipulatePropsFunction, defineCustomElement22);
var FollowSwan = createReactComponent("taro-follow-swan-core", void 0, manipulatePropsFunction, defineCustomElement23);
var Form = createReactComponent("taro-form-core", void 0, manipulatePropsFunction, defineCustomElement24);
var FunctionalPageNavigator = createReactComponent("taro-functional-page-navigator-core", void 0, manipulatePropsFunction, defineCustomElement25);
var GridBuilder = createReactComponent("taro-grid-builder-core", void 0, manipulatePropsFunction, defineCustomElement26);
var GridView = createReactComponent("taro-grid-view-core", void 0, manipulatePropsFunction, defineCustomElement27);
var Icon = createReactComponent("taro-icon-core", void 0, manipulatePropsFunction, defineCustomElement28);
var Image = createReactComponent("taro-image-core", void 0, manipulatePropsFunction, defineCustomElement29);
var InlinePaymentPanel = createReactComponent("taro-inline-payment-panel-core", void 0, manipulatePropsFunction, defineCustomElement30);
var Input = createReactComponent("taro-input-core", void 0, manipulatePropsFunction, defineCustomElement31);
var KeyboardAccessory = createReactComponent("taro-keyboard-accessory-core", void 0, manipulatePropsFunction, defineCustomElement32);
var Label = createReactComponent("taro-label-core", void 0, manipulatePropsFunction, defineCustomElement33);
var Lifestyle = createReactComponent("taro-lifestyle-core", void 0, manipulatePropsFunction, defineCustomElement34);
var Like = createReactComponent("taro-like-core", void 0, manipulatePropsFunction, defineCustomElement35);
var ListBuilder = createReactComponent("taro-list-builder-core", void 0, manipulatePropsFunction, defineCustomElement36);
var ListView = createReactComponent("taro-list-view-core", void 0, manipulatePropsFunction, defineCustomElement37);
var LivePlayer = createReactComponent("taro-live-player-core", void 0, manipulatePropsFunction, defineCustomElement38);
var LivePusher = createReactComponent("taro-live-pusher-core", void 0, manipulatePropsFunction, defineCustomElement39);
var Login = createReactComponent("taro-login-core", void 0, manipulatePropsFunction, defineCustomElement40);
var Lottie = createReactComponent("taro-lottie-core", void 0, manipulatePropsFunction, defineCustomElement41);
var Map2 = createReactComponent("taro-map-core", void 0, manipulatePropsFunction, defineCustomElement42);
var MatchMedia = createReactComponent("taro-match-media-core", void 0, manipulatePropsFunction, defineCustomElement43);
var MovableArea = createReactComponent("taro-movable-area-core", void 0, manipulatePropsFunction, defineCustomElement44);
var MovableView = createReactComponent("taro-movable-view-core", void 0, manipulatePropsFunction, defineCustomElement45);
var NativeSlot = createReactComponent("taro-native-slot-core", void 0, manipulatePropsFunction, defineCustomElement46);
var NavigationBar = createReactComponent("taro-navigation-bar-core", void 0, manipulatePropsFunction, defineCustomElement47);
var Navigator = createReactComponent("taro-navigator-core", void 0, manipulatePropsFunction, defineCustomElement48);
var NestedScrollBody = createReactComponent("taro-nested-scroll-body-core", void 0, manipulatePropsFunction, defineCustomElement49);
var NestedScrollHeader = createReactComponent("taro-nested-scroll-header-core", void 0, manipulatePropsFunction, defineCustomElement50);
var OfficialAccount = createReactComponent("taro-official-account-core", void 0, manipulatePropsFunction, defineCustomElement51);
var OpenContainer = createReactComponent("taro-open-container-core", void 0, manipulatePropsFunction, defineCustomElement52);
var OpenData = createReactComponent("taro-open-data-core", void 0, manipulatePropsFunction, defineCustomElement53);
var PageContainer = createReactComponent("taro-page-container-core", void 0, manipulatePropsFunction, defineCustomElement54);
var PageMeta = createReactComponent("taro-page-meta-core", void 0, manipulatePropsFunction, defineCustomElement55);
var Picker = createReactComponent("taro-picker-core", void 0, manipulatePropsFunction, defineCustomElement56);
var PickerGroup = createReactComponent("taro-picker-group", void 0, manipulatePropsFunction, defineCustomElement57);
var PickerViewColumn = createReactComponent("taro-picker-view-column-core", void 0, manipulatePropsFunction, defineCustomElement58);
var PickerView = createReactComponent("taro-picker-view-core", void 0, manipulatePropsFunction, defineCustomElement59);
var Progress = createReactComponent("taro-progress-core", void 0, manipulatePropsFunction, defineCustomElement60);
var PullToRefresh = createReactComponent("taro-pull-to-refresh-core", void 0, manipulatePropsFunction, defineCustomElement61);
var Radio = createReactComponent("taro-radio-core", void 0, manipulatePropsFunction, defineCustomElement62);
var RadioGroup = createReactComponent("taro-radio-group-core", void 0, manipulatePropsFunction, defineCustomElement63);
var RichText = createReactComponent("taro-rich-text-core", void 0, manipulatePropsFunction, defineCustomElement64);
var RootPortal = createReactComponent("taro-root-portal-core", void 0, manipulatePropsFunction, defineCustomElement65);
var RtcRoom = createReactComponent("taro-rtc-room-core", void 0, manipulatePropsFunction, defineCustomElement66);
var RtcRoomItem = createReactComponent("taro-rtc-room-item-core", void 0, manipulatePropsFunction, defineCustomElement67);
var ScrollView = createReactComponent("taro-scroll-view-core", void 0, manipulatePropsFunction, defineCustomElement68);
var ShareElement = createReactComponent("taro-share-element-core", void 0, manipulatePropsFunction, defineCustomElement69);
var Slider = createReactComponent("taro-slider-core", void 0, manipulatePropsFunction, defineCustomElement70);
var Slot = createReactComponent("taro-slot-core", void 0, manipulatePropsFunction, defineCustomElement71);
var Snapshot = createReactComponent("taro-snapshot-core", void 0, manipulatePropsFunction, defineCustomElement72);
var Span = createReactComponent("taro-span-core", void 0, manipulatePropsFunction, defineCustomElement73);
var StickyHeader = createReactComponent("taro-sticky-header-core", void 0, manipulatePropsFunction, defineCustomElement74);
var StickySection = createReactComponent("taro-sticky-section-core", void 0, manipulatePropsFunction, defineCustomElement75);
var Swiper = createReactComponent("taro-swiper-core", void 0, manipulatePropsFunction, defineCustomElement76);
var SwiperItem = createReactComponent("taro-swiper-item-core", void 0, manipulatePropsFunction, defineCustomElement77);
var Switch = createReactComponent("taro-switch-core", void 0, manipulatePropsFunction, defineCustomElement78);
var TabItem = createReactComponent("taro-tab-item-core", void 0, manipulatePropsFunction, defineCustomElement79);
var Tabbar = createReactComponent("taro-tabbar", void 0, manipulatePropsFunction, defineCustomElement80);
var Tabs = createReactComponent("taro-tabs-core", void 0, manipulatePropsFunction, defineCustomElement81);
var Text = createReactComponent("taro-text-core", void 0, manipulatePropsFunction, defineCustomElement82);
var Textarea = createReactComponent("taro-textarea-core", void 0, manipulatePropsFunction, defineCustomElement83);
var VideoControl = createReactComponent("taro-video-control", void 0, manipulatePropsFunction, defineCustomElement84);
var Video = createReactComponent("taro-video-core", void 0, manipulatePropsFunction, defineCustomElement85);
var VideoDanmu = createReactComponent("taro-video-danmu", void 0, manipulatePropsFunction, defineCustomElement86);
var View = createReactComponent("taro-view-core", void 0, manipulatePropsFunction, defineCustomElement87);
var VoipRoom = createReactComponent("taro-voip-room-core", void 0, manipulatePropsFunction, defineCustomElement88);
var WebView = createReactComponent("taro-web-view-core", void 0, manipulatePropsFunction, defineCustomElement89);
export { Ad, AdCustom, AnimationVideo, AnimationView, ArCamera, Audio, AwemeData, Block, Button, Camera, Canvas, ChannelLive, ChannelVideo, Checkbox, CheckboxGroup, CommentDetail, CommentList, ContactButton, CoverImage, CoverView, CustomWrapper, DraggableSheet, Editor, FollowSwan, Form, FunctionalPageNavigator, GridBuilder, GridView, Icon, Image, InlinePaymentPanel, Input, KeyboardAccessory, Label, Lifestyle, Like, ListBuilder, ListView, LivePlayer, LivePusher, Login, Lottie, Map2 as Map, MatchMedia, MovableArea, MovableView, NativeSlot, NavigationBar, Navigator, NestedScrollBody, NestedScrollHeader, OfficialAccount, OpenContainer, OpenData, PageContainer, PageMeta, Picker, PickerGroup, PickerView, PickerViewColumn, Progress, PullToRefresh, Radio, RadioGroup, RichText, RootPortal, RtcRoom, RtcRoomItem, ScrollView, ShareElement, Slider, Slot, Snapshot, Span, StickyHeader, StickySection, Swiper, SwiperItem, Switch, TabItem, Tabbar, Tabs, Text, Textarea, Video, VideoControl, VideoDanmu, View, VoipRoom, WebView };
