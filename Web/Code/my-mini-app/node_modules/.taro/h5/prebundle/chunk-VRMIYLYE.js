// node_modules/@tarojs/shared/dist/shared.esm.js
function _array_like_to_array(arr, len) {
    if (len == null || len > arr.length) len = arr.length;
    for(var i = 0, arr2 = new Array(len); i < len; i++)arr2[i] = arr[i];
    return arr2;
}
function _array_without_holes(arr) {
    if (Array.isArray(arr)) return _array_like_to_array(arr);
}
function _assert_this_initialized(self) {
    if (self === void 0) {
        throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
    }
    return self;
}
function _class_call_check(instance, Constructor) {
    if (!(instance instanceof Constructor)) {
        throw new TypeError("Cannot call a class as a function");
    }
}
function _defineProperties(target, props) {
    for(var i = 0; i < props.length; i++){
        var descriptor = props[i];
        descriptor.enumerable = descriptor.enumerable || false;
        descriptor.configurable = true;
        if ("value" in descriptor) descriptor.writable = true;
        Object.defineProperty(target, descriptor.key, descriptor);
    }
}
function _create_class(Constructor, protoProps, staticProps) {
    if (protoProps) _defineProperties(Constructor.prototype, protoProps);
    if (staticProps) _defineProperties(Constructor, staticProps);
    return Constructor;
}
function _get(target, property, receiver) {
    if (typeof Reflect !== "undefined" && Reflect.get) {
        _get = Reflect.get;
    } else {
        _get = function get(target, property, receiver) {
            var base = _super_prop_base(target, property);
            if (!base) return;
            var desc = Object.getOwnPropertyDescriptor(base, property);
            if (desc.get) {
                return desc.get.call(receiver || target);
            }
            return desc.value;
        };
    }
    return _get(target, property, receiver || target);
}
function _get_prototype_of(o) {
    _get_prototype_of = Object.setPrototypeOf ? Object.getPrototypeOf : function getPrototypeOf(o) {
        return o.__proto__ || Object.getPrototypeOf(o);
    };
    return _get_prototype_of(o);
}
function _inherits(subClass, superClass) {
    if (typeof superClass !== "function" && superClass !== null) {
        throw new TypeError("Super expression must either be null or a function");
    }
    subClass.prototype = Object.create(superClass && superClass.prototype, {
        constructor: {
            value: subClass,
            writable: true,
            configurable: true
        }
    });
    if (superClass) _set_prototype_of(subClass, superClass);
}
function _iterable_to_array(iter) {
    if (typeof Symbol !== "undefined" && iter[Symbol.iterator] != null || iter["@@iterator"] != null) return Array.from(iter);
}
function _non_iterable_spread() {
    throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}
function _possible_constructor_return(self, call) {
    if (call && (_type_of(call) === "object" || typeof call === "function")) {
        return call;
    }
    return _assert_this_initialized(self);
}
function _set_prototype_of(o, p) {
    _set_prototype_of = Object.setPrototypeOf || function setPrototypeOf(o, p) {
        o.__proto__ = p;
        return o;
    };
    return _set_prototype_of(o, p);
}
function _super_prop_base(object, property) {
    while(!Object.prototype.hasOwnProperty.call(object, property)){
        object = _get_prototype_of(object);
        if (object === null) break;
    }
    return object;
}
function _to_consumable_array(arr) {
    return _array_without_holes(arr) || _iterable_to_array(arr) || _unsupported_iterable_to_array(arr) || _non_iterable_spread();
}
function _type_of(obj) {
    "@swc/helpers - typeof";
    return obj && typeof Symbol !== "undefined" && obj.constructor === Symbol ? "symbol" : typeof obj;
}
function _unsupported_iterable_to_array(o, minLen) {
    if (!o) return;
    if (typeof o === "string") return _array_like_to_array(o, minLen);
    var n = Object.prototype.toString.call(o).slice(8, -1);
    if (n === "Object" && o.constructor) n = o.constructor.name;
    if (n === "Map" || n === "Set") return Array.from(n);
    if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _array_like_to_array(o, minLen);
}
function _is_native_reflect_construct() {
    if (typeof Reflect === "undefined" || !Reflect.construct) return false;
    if (Reflect.construct.sham) return false;
    if (typeof Proxy === "function") return true;
    try {
        Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function() {}));
        return true;
    } catch (e) {
        return false;
    }
}
function _create_super(Derived) {
    var hasNativeReflectConstruct = _is_native_reflect_construct();
    return function _createSuperInternal() {
        var Super = _get_prototype_of(Derived), result;
        if (hasNativeReflectConstruct) {
            var NewTarget = _get_prototype_of(this).constructor;
            result = Reflect.construct(Super, arguments, NewTarget);
        } else {
            result = Super.apply(this, arguments);
        }
        return _possible_constructor_return(this, result);
    };
}
var DEFAULT_EMPTY_ARRAY = "[]";
var NO_DEFAULT_VALUE = "";
var DEFAULT_TRUE = "!0";
var DEFAULT_FALSE = "!1";
var touchEvents = {
    bindTouchStart: NO_DEFAULT_VALUE,
    bindTouchMove: NO_DEFAULT_VALUE,
    bindTouchEnd: NO_DEFAULT_VALUE,
    bindTouchCancel: NO_DEFAULT_VALUE,
    bindLongTap: NO_DEFAULT_VALUE
};
var animation = {
    animation: NO_DEFAULT_VALUE,
    bindAnimationStart: NO_DEFAULT_VALUE,
    bindAnimationIteration: NO_DEFAULT_VALUE,
    bindAnimationEnd: NO_DEFAULT_VALUE,
    bindTransitionEnd: NO_DEFAULT_VALUE
};
function singleQuote(s) {
    return "'".concat(s, "'");
}
var View = Object.assign(Object.assign({
    "hover-class": singleQuote("none"),
    "hover-stop-propagation": DEFAULT_FALSE,
    "hover-start-time": "50",
    "hover-stay-time": "400"
}, touchEvents), animation);
var Icon = {
    type: NO_DEFAULT_VALUE,
    size: "23",
    color: NO_DEFAULT_VALUE
};
var MapComp = Object.assign({
    longitude: NO_DEFAULT_VALUE,
    latitude: NO_DEFAULT_VALUE,
    scale: "16",
    markers: DEFAULT_EMPTY_ARRAY,
    covers: NO_DEFAULT_VALUE,
    polyline: DEFAULT_EMPTY_ARRAY,
    circles: DEFAULT_EMPTY_ARRAY,
    controls: DEFAULT_EMPTY_ARRAY,
    "include-points": DEFAULT_EMPTY_ARRAY,
    "show-location": NO_DEFAULT_VALUE,
    "layer-style": "1",
    bindMarkerTap: NO_DEFAULT_VALUE,
    bindControlTap: NO_DEFAULT_VALUE,
    bindCalloutTap: NO_DEFAULT_VALUE,
    bindUpdated: NO_DEFAULT_VALUE
}, touchEvents);
var Progress = {
    percent: NO_DEFAULT_VALUE,
    "stroke-width": "6",
    color: singleQuote("#09BB07"),
    activeColor: singleQuote("#09BB07"),
    backgroundColor: singleQuote("#EBEBEB"),
    active: DEFAULT_FALSE,
    "active-mode": singleQuote("backwards"),
    "show-info": DEFAULT_FALSE
};
var RichText = {
    nodes: DEFAULT_EMPTY_ARRAY
};
var Text = Object.assign({
    selectable: DEFAULT_FALSE,
    space: NO_DEFAULT_VALUE,
    decode: DEFAULT_FALSE
}, touchEvents);
var Button = Object.assign({
    size: singleQuote("default"),
    type: NO_DEFAULT_VALUE,
    plain: DEFAULT_FALSE,
    disabled: NO_DEFAULT_VALUE,
    loading: DEFAULT_FALSE,
    "form-type": NO_DEFAULT_VALUE,
    "open-type": NO_DEFAULT_VALUE,
    "hover-class": singleQuote("button-hover"),
    "hover-stop-propagation": DEFAULT_FALSE,
    "hover-start-time": "20",
    "hover-stay-time": "70",
    name: NO_DEFAULT_VALUE,
    bindagreeprivacyauthorization: NO_DEFAULT_VALUE
}, touchEvents);
var Checkbox = {
    value: NO_DEFAULT_VALUE,
    disabled: NO_DEFAULT_VALUE,
    checked: DEFAULT_FALSE,
    color: singleQuote("#09BB07"),
    name: NO_DEFAULT_VALUE
};
var CheckboxGroup = {
    bindChange: NO_DEFAULT_VALUE,
    name: NO_DEFAULT_VALUE
};
var Form = {
    "report-submit": DEFAULT_FALSE,
    bindSubmit: NO_DEFAULT_VALUE,
    bindReset: NO_DEFAULT_VALUE,
    name: NO_DEFAULT_VALUE
};
var Input = {
    value: NO_DEFAULT_VALUE,
    type: singleQuote(NO_DEFAULT_VALUE),
    password: DEFAULT_FALSE,
    placeholder: NO_DEFAULT_VALUE,
    "placeholder-style": NO_DEFAULT_VALUE,
    "placeholder-class": singleQuote("input-placeholder"),
    disabled: NO_DEFAULT_VALUE,
    maxlength: "140",
    "cursor-spacing": "0",
    focus: DEFAULT_FALSE,
    "confirm-type": singleQuote("done"),
    "confirm-hold": DEFAULT_FALSE,
    cursor: "-1",
    "selection-start": "-1",
    "selection-end": "-1",
    bindInput: NO_DEFAULT_VALUE,
    bindFocus: NO_DEFAULT_VALUE,
    bindBlur: NO_DEFAULT_VALUE,
    bindConfirm: NO_DEFAULT_VALUE,
    name: NO_DEFAULT_VALUE
};
var Label = Object.assign({
    for: NO_DEFAULT_VALUE,
    name: NO_DEFAULT_VALUE
}, touchEvents);
var Picker = {
    mode: singleQuote("selector"),
    disabled: NO_DEFAULT_VALUE,
    range: NO_DEFAULT_VALUE,
    "range-key": NO_DEFAULT_VALUE,
    value: NO_DEFAULT_VALUE,
    start: NO_DEFAULT_VALUE,
    end: NO_DEFAULT_VALUE,
    fields: singleQuote("day"),
    "custom-item": NO_DEFAULT_VALUE,
    name: NO_DEFAULT_VALUE,
    bindCancel: NO_DEFAULT_VALUE,
    bindChange: NO_DEFAULT_VALUE,
    bindColumnChange: NO_DEFAULT_VALUE
};
var PickerView = {
    value: NO_DEFAULT_VALUE,
    "indicator-style": NO_DEFAULT_VALUE,
    "indicator-class": NO_DEFAULT_VALUE,
    "mask-style": NO_DEFAULT_VALUE,
    "mask-class": NO_DEFAULT_VALUE,
    bindChange: NO_DEFAULT_VALUE,
    name: NO_DEFAULT_VALUE
};
var PickerViewColumn = {
    name: NO_DEFAULT_VALUE
};
var Radio = {
    value: NO_DEFAULT_VALUE,
    checked: DEFAULT_FALSE,
    disabled: NO_DEFAULT_VALUE,
    color: singleQuote("#09BB07"),
    name: NO_DEFAULT_VALUE
};
var RadioGroup = {
    bindChange: NO_DEFAULT_VALUE,
    name: NO_DEFAULT_VALUE
};
var Slider = {
    min: "0",
    max: "100",
    step: "1",
    disabled: NO_DEFAULT_VALUE,
    value: "0",
    activeColor: singleQuote("#1aad19"),
    backgroundColor: singleQuote("#e9e9e9"),
    "block-size": "28",
    "block-color": singleQuote("#ffffff"),
    "show-value": DEFAULT_FALSE,
    bindChange: NO_DEFAULT_VALUE,
    bindChanging: NO_DEFAULT_VALUE,
    name: NO_DEFAULT_VALUE
};
var Switch = {
    checked: DEFAULT_FALSE,
    disabled: NO_DEFAULT_VALUE,
    type: singleQuote("switch"),
    color: singleQuote("#04BE02"),
    bindChange: NO_DEFAULT_VALUE,
    name: NO_DEFAULT_VALUE
};
var Textarea = {
    value: NO_DEFAULT_VALUE,
    placeholder: NO_DEFAULT_VALUE,
    "placeholder-style": NO_DEFAULT_VALUE,
    "placeholder-class": singleQuote("textarea-placeholder"),
    disabled: NO_DEFAULT_VALUE,
    maxlength: "140",
    "auto-focus": DEFAULT_FALSE,
    focus: DEFAULT_FALSE,
    "auto-height": DEFAULT_FALSE,
    fixed: DEFAULT_FALSE,
    "cursor-spacing": "0",
    cursor: "-1",
    "selection-start": "-1",
    "selection-end": "-1",
    bindFocus: NO_DEFAULT_VALUE,
    bindBlur: NO_DEFAULT_VALUE,
    bindLineChange: NO_DEFAULT_VALUE,
    bindInput: NO_DEFAULT_VALUE,
    bindConfirm: NO_DEFAULT_VALUE,
    name: NO_DEFAULT_VALUE
};
var CoverImage = {
    src: NO_DEFAULT_VALUE,
    bindLoad: "eh",
    bindError: "eh"
};
var CoverView = Object.assign({
    "scroll-top": DEFAULT_FALSE
}, touchEvents);
var MovableArea = {
    "scale-area": DEFAULT_FALSE
};
var MovableView = Object.assign(Object.assign({
    direction: "none",
    inertia: DEFAULT_FALSE,
    "out-of-bounds": DEFAULT_FALSE,
    x: NO_DEFAULT_VALUE,
    y: NO_DEFAULT_VALUE,
    damping: "20",
    friction: "2",
    disabled: NO_DEFAULT_VALUE,
    scale: DEFAULT_FALSE,
    "scale-min": "0.5",
    "scale-max": "10",
    "scale-value": "1",
    bindChange: NO_DEFAULT_VALUE,
    bindScale: NO_DEFAULT_VALUE,
    bindHTouchMove: NO_DEFAULT_VALUE,
    bindVTouchMove: NO_DEFAULT_VALUE,
    width: singleQuote("10px"),
    height: singleQuote("10px")
}, touchEvents), animation);
var ScrollView = Object.assign(Object.assign({
    "scroll-x": DEFAULT_FALSE,
    "scroll-y": DEFAULT_FALSE,
    "upper-threshold": "50",
    "lower-threshold": "50",
    "scroll-top": NO_DEFAULT_VALUE,
    "scroll-left": NO_DEFAULT_VALUE,
    "scroll-into-view": NO_DEFAULT_VALUE,
    "scroll-with-animation": DEFAULT_FALSE,
    "enable-back-to-top": DEFAULT_FALSE,
    bindScrollToUpper: NO_DEFAULT_VALUE,
    bindScrollToLower: NO_DEFAULT_VALUE,
    bindScroll: NO_DEFAULT_VALUE
}, touchEvents), animation);
var Swiper = Object.assign({
    "indicator-dots": DEFAULT_FALSE,
    "indicator-color": singleQuote("rgba(0, 0, 0, .3)"),
    "indicator-active-color": singleQuote("#000000"),
    autoplay: DEFAULT_FALSE,
    current: "0",
    interval: "5000",
    duration: "500",
    circular: DEFAULT_FALSE,
    vertical: DEFAULT_FALSE,
    "previous-margin": singleQuote("0px"),
    "next-margin": singleQuote("0px"),
    "display-multiple-items": "1",
    bindChange: NO_DEFAULT_VALUE,
    bindTransition: NO_DEFAULT_VALUE,
    bindAnimationFinish: NO_DEFAULT_VALUE
}, touchEvents);
var SwiperItem = {
    "item-id": NO_DEFAULT_VALUE
};
var Navigator = {
    url: NO_DEFAULT_VALUE,
    "open-type": singleQuote("navigate"),
    delta: "1",
    "hover-class": singleQuote("navigator-hover"),
    "hover-stop-propagation": DEFAULT_FALSE,
    "hover-start-time": "50",
    "hover-stay-time": "600",
    bindSuccess: NO_DEFAULT_VALUE,
    bindFail: NO_DEFAULT_VALUE,
    bindComplete: NO_DEFAULT_VALUE
};
var Audio = {
    id: NO_DEFAULT_VALUE,
    src: NO_DEFAULT_VALUE,
    loop: DEFAULT_FALSE,
    controls: DEFAULT_FALSE,
    poster: NO_DEFAULT_VALUE,
    name: NO_DEFAULT_VALUE,
    author: NO_DEFAULT_VALUE,
    bindError: NO_DEFAULT_VALUE,
    bindPlay: NO_DEFAULT_VALUE,
    bindPause: NO_DEFAULT_VALUE,
    bindTimeUpdate: NO_DEFAULT_VALUE,
    bindEnded: NO_DEFAULT_VALUE
};
var Camera = {
    "device-position": singleQuote("back"),
    flash: singleQuote("auto"),
    bindStop: NO_DEFAULT_VALUE,
    bindError: NO_DEFAULT_VALUE
};
var Image = Object.assign({
    src: NO_DEFAULT_VALUE,
    mode: singleQuote("scaleToFill"),
    "lazy-load": DEFAULT_FALSE,
    bindError: NO_DEFAULT_VALUE,
    bindLoad: NO_DEFAULT_VALUE
}, touchEvents);
var LivePlayer = Object.assign({
    src: NO_DEFAULT_VALUE,
    autoplay: DEFAULT_FALSE,
    muted: DEFAULT_FALSE,
    orientation: singleQuote("vertical"),
    "object-fit": singleQuote("contain"),
    "background-mute": DEFAULT_FALSE,
    "min-cache": "1",
    "max-cache": "3",
    bindStateChange: NO_DEFAULT_VALUE,
    bindFullScreenChange: NO_DEFAULT_VALUE,
    bindNetStatus: NO_DEFAULT_VALUE
}, animation);
var Video = Object.assign({
    src: NO_DEFAULT_VALUE,
    duration: NO_DEFAULT_VALUE,
    controls: DEFAULT_TRUE,
    "danmu-list": NO_DEFAULT_VALUE,
    "danmu-btn": NO_DEFAULT_VALUE,
    "enable-danmu": NO_DEFAULT_VALUE,
    autoplay: DEFAULT_FALSE,
    loop: DEFAULT_FALSE,
    muted: DEFAULT_FALSE,
    "initial-time": "0",
    "page-gesture": DEFAULT_FALSE,
    direction: NO_DEFAULT_VALUE,
    "show-progress": DEFAULT_TRUE,
    "show-fullscreen-btn": DEFAULT_TRUE,
    "show-play-btn": DEFAULT_TRUE,
    "show-center-play-btn": DEFAULT_TRUE,
    "enable-progress-gesture": DEFAULT_TRUE,
    "object-fit": singleQuote("contain"),
    poster: NO_DEFAULT_VALUE,
    "show-mute-btn": DEFAULT_FALSE,
    bindPlay: NO_DEFAULT_VALUE,
    bindPause: NO_DEFAULT_VALUE,
    bindEnded: NO_DEFAULT_VALUE,
    bindTimeUpdate: NO_DEFAULT_VALUE,
    bindFullScreenChange: NO_DEFAULT_VALUE,
    bindWaiting: NO_DEFAULT_VALUE,
    bindError: NO_DEFAULT_VALUE
}, animation);
var Canvas = Object.assign({
    "canvas-id": NO_DEFAULT_VALUE,
    "disable-scroll": DEFAULT_FALSE,
    bindError: NO_DEFAULT_VALUE
}, touchEvents);
var Ad = {
    "unit-id": NO_DEFAULT_VALUE,
    "ad-intervals": NO_DEFAULT_VALUE,
    bindLoad: NO_DEFAULT_VALUE,
    bindError: NO_DEFAULT_VALUE,
    bindClose: NO_DEFAULT_VALUE
};
var WebView = {
    src: NO_DEFAULT_VALUE,
    bindMessage: NO_DEFAULT_VALUE,
    bindLoad: NO_DEFAULT_VALUE,
    bindError: NO_DEFAULT_VALUE
};
var Block = {};
var SlotView = {
    name: NO_DEFAULT_VALUE
};
var Slot = {
    name: NO_DEFAULT_VALUE
};
var NativeSlot = {
    name: NO_DEFAULT_VALUE
};
var Script = {};
var internalComponents = {
    View: View,
    Icon: Icon,
    Progress: Progress,
    RichText: RichText,
    Text: Text,
    Button: Button,
    Checkbox: Checkbox,
    CheckboxGroup: CheckboxGroup,
    Form: Form,
    Input: Input,
    Label: Label,
    Picker: Picker,
    PickerView: PickerView,
    PickerViewColumn: PickerViewColumn,
    Radio: Radio,
    RadioGroup: RadioGroup,
    Slider: Slider,
    Switch: Switch,
    CoverImage: CoverImage,
    Textarea: Textarea,
    CoverView: CoverView,
    MovableArea: MovableArea,
    MovableView: MovableView,
    ScrollView: ScrollView,
    Swiper: Swiper,
    SwiperItem: SwiperItem,
    Navigator: Navigator,
    Audio: Audio,
    Camera: Camera,
    Image: Image,
    LivePlayer: LivePlayer,
    Video: Video,
    Canvas: Canvas,
    Ad: Ad,
    WebView: WebView,
    Block: Block,
    Map: MapComp,
    Slot: Slot,
    SlotView: SlotView,
    NativeSlot: NativeSlot,
    Script: Script
};
var controlledComponent = /* @__PURE__ */ new Set([
    "input",
    "checkbox",
    "picker",
    "picker-view",
    "radio",
    "slider",
    "switch",
    "textarea"
]);
var focusComponents = /* @__PURE__ */ new Set([
    "input",
    "textarea"
]);
var voidElements = /* @__PURE__ */ new Set([
    "progress",
    "icon",
    "rich-text",
    "input",
    "textarea",
    "slider",
    "switch",
    "audio",
    "ad",
    "official-account",
    "open-data",
    "navigation-bar"
]);
var nestElements = /* @__PURE__ */ new Map([
    [
        "view",
        -1
    ],
    [
        "catch-view",
        -1
    ],
    [
        "cover-view",
        -1
    ],
    [
        "static-view",
        -1
    ],
    [
        "pure-view",
        -1
    ],
    [
        "block",
        -1
    ],
    [
        "text",
        -1
    ],
    [
        "static-text",
        6
    ],
    [
        "slot",
        8
    ],
    [
        "slot-view",
        8
    ],
    [
        "label",
        6
    ],
    [
        "form",
        4
    ],
    [
        "scroll-view",
        4
    ],
    [
        "swiper",
        4
    ],
    [
        "swiper-item",
        4
    ]
]);
var PLATFORM_TYPE;
(function(PLATFORM_TYPE2) {
    PLATFORM_TYPE2["MINI"] = "mini";
    PLATFORM_TYPE2["WEB"] = "web";
    PLATFORM_TYPE2["RN"] = "rn";
    PLATFORM_TYPE2["HARMONY"] = "harmony";
    PLATFORM_TYPE2["QUICK"] = "quickapp";
})(PLATFORM_TYPE || (PLATFORM_TYPE = {}));
var PLATFORM_CONFIG_MAP = {
    h5: {
        type: PLATFORM_TYPE.WEB
    },
    harmony: {
        type: PLATFORM_TYPE.HARMONY
    },
    mini: {
        type: PLATFORM_TYPE.MINI
    },
    rn: {
        type: PLATFORM_TYPE.RN
    },
    quickapp: {
        type: PLATFORM_TYPE.QUICK
    }
};
var Events = /*#__PURE__*/ function() {
    "use strict";
    function _Events(opts) {
        _class_call_check(this, _Events);
        var _a;
        this.callbacks = (_a = opts === null || opts === void 0 ? void 0 : opts.callbacks) !== null && _a !== void 0 ? _a : {};
    }
    _create_class(_Events, [
        {
            key: "on",
            value: function on(eventName, callback, context) {
                var event, tail, _eventName;
                if (!callback) {
                    return this;
                }
                if ((typeof eventName === "undefined" ? "undefined" : _type_of(eventName)) === "symbol") {
                    _eventName = [
                        eventName
                    ];
                } else {
                    _eventName = eventName.split(_Events.eventSplitter);
                }
                this.callbacks || (this.callbacks = {});
                var calls = this.callbacks;
                while(event = _eventName.shift()){
                    var list = calls[event];
                    var node = list ? list.tail : {};
                    node.next = tail = {};
                    node.context = context;
                    node.callback = callback;
                    calls[event] = {
                        tail: tail,
                        next: list ? list.next : node
                    };
                }
                return this;
            }
        },
        {
            key: "once",
            value: function once(events, callback, context) {
                var _this = this;
                var wrapper = function() {
                    for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){
                        args[_key] = arguments[_key];
                    }
                    callback.apply(_this, args);
                    _this.off(events, wrapper, context);
                };
                this.on(events, wrapper, context);
                return this;
            }
        },
        {
            key: "off",
            value: function off(events, callback, context) {
                var event, calls, _events;
                if (!(calls = this.callbacks)) {
                    return this;
                }
                if (!(events || callback || context)) {
                    delete this.callbacks;
                    return this;
                }
                if ((typeof events === "undefined" ? "undefined" : _type_of(events)) === "symbol") {
                    _events = [
                        events
                    ];
                } else {
                    _events = events ? events.split(_Events.eventSplitter) : Object.keys(calls);
                }
                while(event = _events.shift()){
                    var node = calls[event];
                    delete calls[event];
                    if (!node || !(callback || context)) {
                        continue;
                    }
                    var tail = node.tail;
                    while((node = node.next) !== tail){
                        var cb = node.callback;
                        var ctx = node.context;
                        if (callback && cb !== callback || context && ctx !== context) {
                            this.on(event, cb, ctx);
                        }
                    }
                }
                return this;
            }
        },
        {
            key: "trigger",
            value: function trigger(events) {
                for(var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){
                    args[_key - 1] = arguments[_key];
                }
                var event, node, calls, _events;
                if (!(calls = this.callbacks)) {
                    return this;
                }
                if ((typeof events === "undefined" ? "undefined" : _type_of(events)) === "symbol") {
                    _events = [
                        events
                    ];
                } else {
                    _events = events.split(_Events.eventSplitter);
                }
                while(event = _events.shift()){
                    if (node = calls[event]) {
                        var tail = node.tail;
                        while((node = node.next) !== tail){
                            node.callback.apply(node.context || this, args);
                        }
                    }
                }
                return this;
            }
        }
    ]);
    return _Events;
}();
Events.eventSplitter = ",";
var PageEvts = /*#__PURE__*/ function(Events) {
    "use strict";
    _inherits(PageEvts, Events);
    var _super = _create_super(PageEvts);
    function PageEvts() {
        _class_call_check(this, PageEvts);
        var _this;
        _this = _super.call.apply(_super, [
            this
        ].concat(Array.prototype.slice.call(arguments)));
        _this.exeList = [];
        return _this;
    }
    _create_class(PageEvts, [
        {
            key: "on",
            value: function on(eventName, callback) {
                var _this = this;
                _get(_get_prototype_of(PageEvts.prototype), "on", this).call(this, eventName, callback, this);
                this.exeList = this.exeList.reduce(function(prev, item) {
                    if (item.eventName === eventName) {
                        _get(_get_prototype_of(PageEvts.prototype), "trigger", _this).call(_this, item.eventName, item.data);
                    } else {
                        prev.push(item);
                    }
                    return prev;
                }, []);
                return this;
            }
        },
        {
            key: "emit",
            value: function emit(events, data) {
                routeChannel.trigger(events, data);
            }
        }
    ]);
    return PageEvts;
}(Events);
var pageChannel = new PageEvts();
var RouteEvts = /*#__PURE__*/ function(Events) {
    "use strict";
    _inherits(RouteEvts, Events);
    var _super = _create_super(RouteEvts);
    function RouteEvts() {
        _class_call_check(this, RouteEvts);
        return _super.apply(this, arguments);
    }
    _create_class(RouteEvts, [
        {
            key: "emit",
            value: function emit(events, data) {
                pageChannel.off(events);
                pageChannel.exeList.push({
                    eventName: events,
                    data: data
                });
            }
        },
        {
            key: "addEvents",
            value: function addEvents(events) {
                var _this = this;
                if (!events || typeof events !== "object") return;
                Object.keys(events).forEach(function(key) {
                    _this.off(key);
                    _this.on(key, events[key], _this);
                });
            }
        }
    ]);
    return RouteEvts;
}(Events);
var routeChannel = new RouteEvts();
var EventChannel = {
    pageChannel: pageChannel,
    routeChannel: routeChannel
};
function isString(o) {
    return typeof o === "string";
}
function isUndefined(o) {
    return typeof o === "undefined";
}
function isNull(o) {
    return o === null;
}
function isObject(o) {
    return o !== null && typeof o === "object";
}
function isBoolean(o) {
    return o === true || o === false;
}
function isFunction(o) {
    return typeof o === "function";
}
function isNumber(o) {
    return typeof o === "number";
}
function isBooleanStringLiteral(o) {
    return o === "true" || o === "false";
}
var isArray = Array.isArray;
var isWebPlatform = function() {
    return true;
};
var HOOK_TYPE;
(function(HOOK_TYPE2) {
    HOOK_TYPE2[HOOK_TYPE2["SINGLE"] = 0] = "SINGLE";
    HOOK_TYPE2[HOOK_TYPE2["MULTI"] = 1] = "MULTI";
    HOOK_TYPE2[HOOK_TYPE2["WATERFALL"] = 2] = "WATERFALL";
})(HOOK_TYPE || (HOOK_TYPE = {}));
var defaultMiniLifecycle = {
    app: [
        "onLaunch",
        "onShow",
        "onHide"
    ],
    page: [
        "onLoad",
        "onUnload",
        "onReady",
        "onShow",
        "onHide",
        [
            "onPullDownRefresh",
            "onReachBottom",
            "onPageScroll",
            "onResize",
            "defer:onTabItemTap",
            "onTitleClick",
            "onOptionMenuClick",
            "onPopMenuClick",
            "onPullIntercept",
            "onAddToFavorites"
        ],
        [
            "onShareAppMessage",
            "onShareTimeline"
        ]
    ],
    component: [
        "attached",
        "detached"
    ]
};
function TaroHook(type, initial) {
    return {
        type: type,
        initial: initial || null
    };
}
var TaroHooks = /*#__PURE__*/ function(Events) {
    "use strict";
    _inherits(TaroHooks, Events);
    var _super = _create_super(TaroHooks);
    function TaroHooks(hooks2, opts) {
        _class_call_check(this, TaroHooks);
        var _this;
        _this = _super.call(this, opts);
        _this.hooks = hooks2;
        for(var hookName in hooks2){
            var initial = hooks2[hookName].initial;
            if (isFunction(initial)) {
                _this.on(hookName, initial);
            }
        }
        return _this;
    }
    _create_class(TaroHooks, [
        {
            key: "tapOneOrMany",
            value: function tapOneOrMany(hookName, callback) {
                var _this = this;
                var list = isFunction(callback) ? [
                    callback
                ] : callback;
                list.forEach(function(cb) {
                    return _this.on(hookName, cb);
                });
            }
        },
        {
            key: "tap",
            value: function tap(hookName, callback) {
                var hooks2 = this.hooks;
                var _hooks2_hookName = hooks2[hookName], type = _hooks2_hookName.type, initial = _hooks2_hookName.initial;
                if (type === HOOK_TYPE.SINGLE) {
                    this.off(hookName);
                    this.on(hookName, isFunction(callback) ? callback : callback[callback.length - 1]);
                } else {
                    initial && this.off(hookName, initial);
                    this.tapOneOrMany(hookName, callback);
                }
            }
        },
        {
            key: "call",
            value: function call(hookName) {
                for(var _len = arguments.length, rest = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){
                    rest[_key - 1] = arguments[_key];
                }
                var _a;
                var hook = this.hooks[hookName];
                if (!hook) return;
                var type = hook.type;
                var calls = this.callbacks;
                if (!calls) return;
                var list = calls[hookName];
                if (list) {
                    var tail = list.tail;
                    var node = list.next;
                    var args = rest;
                    var res;
                    while(node !== tail){
                        res = (_a = node.callback) === null || _a === void 0 ? void 0 : _a.apply(node.context || this, args);
                        if (type === HOOK_TYPE.WATERFALL) {
                            var params = [
                                res
                            ];
                            args = params;
                        }
                        node = node.next;
                    }
                    return res;
                }
            }
        },
        {
            key: "isExist",
            value: function isExist(hookName) {
                var _a;
                return Boolean((_a = this.callbacks) === null || _a === void 0 ? void 0 : _a[hookName]);
            }
        }
    ]);
    return TaroHooks;
}(Events);
var hooks = new TaroHooks({
    getMiniLifecycle: TaroHook(HOOK_TYPE.SINGLE, function(defaultConfig) {
        return defaultConfig;
    }),
    getMiniLifecycleImpl: TaroHook(HOOK_TYPE.SINGLE, function() {
        return this.call("getMiniLifecycle", defaultMiniLifecycle);
    }),
    getLifecycle: TaroHook(HOOK_TYPE.SINGLE, function(instance, lifecycle) {
        return instance[lifecycle];
    }),
    modifyRecursiveComponentConfig: TaroHook(HOOK_TYPE.SINGLE, function(defaultConfig) {
        return defaultConfig;
    }),
    getPathIndex: TaroHook(HOOK_TYPE.SINGLE, function(indexOfNode) {
        return "[".concat(indexOfNode, "]");
    }),
    getEventCenter: TaroHook(HOOK_TYPE.SINGLE, function(Events2) {
        return new Events2();
    }),
    isBubbleEvents: TaroHook(HOOK_TYPE.SINGLE, function(eventName) {
        var BUBBLE_EVENTS = /* @__PURE__ */ new Set([
            "touchstart",
            "touchmove",
            "touchcancel",
            "touchend",
            "touchforcechange",
            "tap",
            "longpress",
            "longtap",
            "transitionend",
            "animationstart",
            "animationiteration",
            "animationend"
        ]);
        return BUBBLE_EVENTS.has(eventName);
    }),
    getSpecialNodes: TaroHook(HOOK_TYPE.SINGLE, function() {
        return [
            "view",
            "text",
            "image"
        ];
    }),
    onRemoveAttribute: TaroHook(HOOK_TYPE.SINGLE),
    batchedEventUpdates: TaroHook(HOOK_TYPE.SINGLE),
    mergePageInstance: TaroHook(HOOK_TYPE.SINGLE),
    modifyPageObject: TaroHook(HOOK_TYPE.SINGLE),
    createPullDownComponent: TaroHook(HOOK_TYPE.SINGLE),
    getDOMNode: TaroHook(HOOK_TYPE.SINGLE),
    modifyHydrateData: TaroHook(HOOK_TYPE.SINGLE),
    transferHydrateData: TaroHook(HOOK_TYPE.SINGLE),
    modifySetAttrPayload: TaroHook(HOOK_TYPE.SINGLE),
    modifyRmAttrPayload: TaroHook(HOOK_TYPE.SINGLE),
    onAddEvent: TaroHook(HOOK_TYPE.SINGLE),
    proxyToRaw: TaroHook(HOOK_TYPE.SINGLE, function(proxyObj) {
        return proxyObj;
    }),
    modifyMpEvent: TaroHook(HOOK_TYPE.MULTI),
    modifyMpEventImpl: TaroHook(HOOK_TYPE.SINGLE, function(e) {
        try {
            this.call("modifyMpEvent", e);
        } catch (error) {
            console.warn("[Taro modifyMpEvent hook Error]: " + (error === null || error === void 0 ? void 0 : error.message));
        }
    }),
    injectNewStyleProperties: TaroHook(HOOK_TYPE.SINGLE),
    modifyTaroEvent: TaroHook(HOOK_TYPE.MULTI),
    dispatchTaroEvent: TaroHook(HOOK_TYPE.SINGLE, function(e, node) {
        node.dispatchEvent(e);
    }),
    dispatchTaroEventFinish: TaroHook(HOOK_TYPE.MULTI),
    modifyTaroEventReturn: TaroHook(HOOK_TYPE.SINGLE, function() {
        return void 0;
    }),
    modifyDispatchEvent: TaroHook(HOOK_TYPE.MULTI),
    initNativeApi: TaroHook(HOOK_TYPE.MULTI),
    patchElement: TaroHook(HOOK_TYPE.MULTI),
    modifyAddEventListener: TaroHook(HOOK_TYPE.SINGLE),
    modifyRemoveEventListener: TaroHook(HOOK_TYPE.SINGLE)
});
var EMPTY_OBJ = {};
var EMPTY_ARR = [];
var noop = function() {
    for(var _len = arguments.length, _ = new Array(_len), _key = 0; _key < _len; _key++){
        _[_key] = arguments[_key];
    }
};
var box = function(v) {
    return {
        v: v
    };
};
var unbox = function(b) {
    return b.v;
};
function toDashed(s) {
    return s.replace(/([a-z0-9])([A-Z])/g, "$1-$2").toLowerCase();
}
function toCamelCase(s) {
    var camel = "";
    var nextCap = false;
    for(var i = 0; i < s.length; i++){
        if (s[i] !== "-") {
            camel += nextCap ? s[i].toUpperCase() : s[i];
            nextCap = false;
        } else {
            nextCap = true;
        }
    }
    return camel;
}
var toKebabCase = function toKebabCase(string) {
    return string.replace(/([a-z])([A-Z])/g, "$1-$2").toLowerCase();
};
function capitalize(s) {
    return s.charAt(0).toUpperCase() + s.slice(1);
}
var hasOwnProperty = Object.prototype.hasOwnProperty;
var hasOwn = function(val, key) {
    return hasOwnProperty.call(val, key);
};
function ensure(condition, msg) {
    if (!condition) {
        if (true) {
            var reportIssue = "\n如有疑问，请提交 issue 至：https://github.com/nervjs/taro/issues";
            throw new Error(msg + reportIssue);
        } else {
            throw new Error(msg);
        }
    }
}
function warn(condition, msg) {
    if (true) {
        if (condition) {
            console.warn("[taro warn] ".concat(msg));
        }
    }
}
function queryToJson(str) {
    var dec = decodeURIComponent;
    var qp = str.split("&");
    var ret = {};
    var name;
    var val;
    for(var i = 0, l = qp.length, item; i < l; ++i){
        item = qp[i];
        if (item.length) {
            var s = item.indexOf("=");
            if (s < 0) {
                name = dec(item);
                val = "";
            } else {
                name = dec(item.slice(0, s));
                val = dec(item.slice(s + 1));
            }
            if (typeof ret[name] === "string") {
                ret[name] = [
                    ret[name]
                ];
            }
            if (Array.isArray(ret[name])) {
                ret[name].push(val);
            } else {
                ret[name] = val;
            }
        }
    }
    return ret;
}
var _uniqueId = 1;
var _loadTime = /* @__PURE__ */ new Date().getTime().toString();
function getUniqueKey() {
    return _loadTime + _uniqueId++;
}
var cacheData = {};
function cacheDataSet(key, val) {
    cacheData[key] = val;
}
function cacheDataGet(key, delelteAfterGet) {
    var temp = cacheData[key];
    delelteAfterGet && delete cacheData[key];
    return temp;
}
function cacheDataHas(key) {
    return key in cacheData;
}
function mergeInternalComponents(components) {
    Object.keys(components).forEach(function(name) {
        if (name in internalComponents) {
            Object.assign(internalComponents[name], components[name]);
        } else {
            internalComponents[name] = components[name];
        }
    });
    return internalComponents;
}
function getComponentsAlias(origin) {
    var mapping = {};
    var viewAttrs = origin.View;
    var extraList = {
        "#text": {},
        StaticView: viewAttrs,
        StaticImage: origin.Image,
        StaticText: origin.Text,
        PureView: viewAttrs,
        CatchView: viewAttrs
    };
    origin = Object.assign(Object.assign({}, origin), extraList);
    Object.keys(origin).sort(function(a, b) {
        var reg = /^(Static|Pure|Catch)*(View|Image|Text)$/;
        var isACommonly = reg.test(a);
        var isBCommonly = reg.test(b);
        if (isACommonly && isBCommonly) {
            return a > b ? 1 : -1;
        } else if (isACommonly) {
            return -1;
        } else if (isBCommonly) {
            return 1;
        } else {
            return a >= b ? 1 : -1;
        }
    }).forEach(function(key, num) {
        var obj = {
            _num: String(num)
        };
        Object.keys(origin[key]).filter(function(attr) {
            return !/^bind/.test(attr) && ![
                "focus",
                "blur"
            ].includes(attr);
        }).sort().forEach(function(attr, index) {
            obj[toCamelCase(attr)] = "p" + index;
        });
        mapping[toDashed(key)] = obj;
    });
    return mapping;
}
function getPlatformType() {
    var platform = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : "weapp", configNameOrType = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : PLATFORM_TYPE.MINI;
    if (Object.keys(PLATFORM_CONFIG_MAP).includes(platform)) {
        configNameOrType = platform;
    }
    var param = PLATFORM_CONFIG_MAP[configNameOrType] || {};
    return param.type || configNameOrType;
}
function mergeReconciler(hostConfig, hooksForTest) {
    var obj = hooksForTest || hooks;
    var keys = Object.keys(hostConfig);
    keys.forEach(function(key) {
        obj.tap(key, hostConfig[key]);
    });
}
function nonsupport(api) {
    return function() {
        console.warn("小程序暂不支持 ".concat(api));
    };
}
function setUniqueKeyToRoute(key, obj) {
    var routerParamsPrivateKey = "__key_";
    var useDataCacheApis = [
        "navigateTo",
        "redirectTo",
        "reLaunch",
        "switchTab"
    ];
    if (useDataCacheApis.indexOf(key) > -1) {
        var url = obj.url = obj.url || "";
        var hasMark = url.indexOf("?") > -1;
        var cacheKey = getUniqueKey();
        obj.url += (hasMark ? "&" : "?") + "".concat(routerParamsPrivateKey, "=").concat(cacheKey);
    }
}
function indent(str, size) {
    return str.split("\n").map(function(line, index) {
        var indent2 = index === 0 ? "" : Array(size).fill(" ").join("");
        return indent2 + line;
    }).join("\n");
}
var needPromiseApis = /* @__PURE__ */ new Set([
    "addPhoneContact",
    "authorize",
    "canvasGetImageData",
    "canvasPutImageData",
    "canvasToTempFilePath",
    "checkSession",
    "chooseAddress",
    "chooseImage",
    "chooseInvoiceTitle",
    "chooseLocation",
    "chooseVideo",
    "clearStorage",
    "closeBLEConnection",
    "closeBluetoothAdapter",
    "closeSocket",
    "compressImage",
    "connectSocket",
    "createBLEConnection",
    "downloadFile",
    "exitMiniProgram",
    "getAvailableAudioSources",
    "getBLEDeviceCharacteristics",
    "getBLEDeviceServices",
    "getBatteryInfo",
    "getBeacons",
    "getBluetoothAdapterState",
    "getBluetoothDevices",
    "getClipboardData",
    "getConnectedBluetoothDevices",
    "getConnectedWifi",
    "getExtConfig",
    "getFileInfo",
    "getImageInfo",
    "getLocation",
    "getNetworkType",
    "getSavedFileInfo",
    "getSavedFileList",
    "getScreenBrightness",
    "getSetting",
    "getStorage",
    "getStorageInfo",
    "getSystemInfo",
    "getUserInfo",
    "getWifiList",
    "hideHomeButton",
    "hideShareMenu",
    "hideTabBar",
    "hideTabBarRedDot",
    "loadFontFace",
    "login",
    "makePhoneCall",
    "navigateBack",
    "navigateBackMiniProgram",
    "navigateTo",
    "navigateToBookshelf",
    "navigateToMiniProgram",
    "notifyBLECharacteristicValueChange",
    "hideKeyboard",
    "hideLoading",
    "hideNavigationBarLoading",
    "hideToast",
    "openBluetoothAdapter",
    "openDocument",
    "openLocation",
    "openSetting",
    "pageScrollTo",
    "previewImage",
    "queryBookshelf",
    "reLaunch",
    "readBLECharacteristicValue",
    "redirectTo",
    "removeSavedFile",
    "removeStorage",
    "removeTabBarBadge",
    "requestSubscribeMessage",
    "saveFile",
    "saveImageToPhotosAlbum",
    "saveVideoToPhotosAlbum",
    "scanCode",
    "sendSocketMessage",
    "setBackgroundColor",
    "setBackgroundTextStyle",
    "setClipboardData",
    "setEnableDebug",
    "setInnerAudioOption",
    "setKeepScreenOn",
    "setNavigationBarColor",
    "setNavigationBarTitle",
    "setScreenBrightness",
    "setStorage",
    "setTabBarBadge",
    "setTabBarItem",
    "setTabBarStyle",
    "showActionSheet",
    "showFavoriteGuide",
    "showLoading",
    "showModal",
    "showShareMenu",
    "showTabBar",
    "showTabBarRedDot",
    "showToast",
    "startBeaconDiscovery",
    "startBluetoothDevicesDiscovery",
    "startDeviceMotionListening",
    "startPullDownRefresh",
    "stopBeaconDiscovery",
    "stopBluetoothDevicesDiscovery",
    "stopCompass",
    "startCompass",
    "startAccelerometer",
    "stopAccelerometer",
    "showNavigationBarLoading",
    "stopDeviceMotionListening",
    "stopPullDownRefresh",
    "switchTab",
    "uploadFile",
    "vibrateLong",
    "vibrateShort",
    "writeBLECharacteristicValue"
]);
function getCanIUseWebp(taro) {
    return function() {
        var _a;
        var res = (_a = taro.getSystemInfoSync) === null || _a === void 0 ? void 0 : _a.call(taro);
        if (!res) {
            if (true) {
                console.error("不支持 API canIUseWebp");
            }
            return false;
        }
        var platform = res.platform;
        var platformLower = platform.toLowerCase();
        if (platformLower === "android" || platformLower === "devtools") {
            return true;
        }
        return false;
    };
}
function getNormalRequest(global) {
    return function request(options) {
        options = options ? isString(options) ? {
            url: options
        } : options : {};
        var originSuccess = options.success;
        var originFail = options.fail;
        var originComplete = options.complete;
        var requestTask;
        var p = new Promise(function(resolve, reject) {
            options.success = function(res) {
                originSuccess && originSuccess(res);
                resolve(res);
            };
            options.fail = function(res) {
                originFail && originFail(res);
                reject(res);
            };
            options.complete = function(res) {
                originComplete && originComplete(res);
            };
            requestTask = global.request(options);
        });
        equipTaskMethodsIntoPromise(requestTask, p);
        p.abort = function(cb) {
            cb && cb();
            if (requestTask) {
                requestTask.abort();
            }
            return p;
        };
        return p;
    };
}
function processApis(taro, global) {
    var config = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};
    var patchNeedPromiseApis = config.needPromiseApis || [];
    var _needPromiseApis = /* @__PURE__ */ new Set(_to_consumable_array(patchNeedPromiseApis).concat(_to_consumable_array(needPromiseApis)));
    var preserved = [
        "getEnv",
        "interceptors",
        "Current",
        "getCurrentInstance",
        "options",
        "nextTick",
        "eventCenter",
        "Events",
        "preload",
        "webpackJsonp"
    ];
    var apis = new Set(!config.isOnlyPromisify ? Object.keys(global).filter(function(api) {
        return preserved.indexOf(api) === -1;
    }) : patchNeedPromiseApis);
    if (config.modifyApis) {
        config.modifyApis(apis);
    }
    apis.forEach(function(key) {
        if (_needPromiseApis.has(key)) {
            var originKey = key;
            taro[originKey] = function() {
                var options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
                for(var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){
                    args[_key - 1] = arguments[_key];
                }
                var key2 = originKey;
                if (typeof options === "string") {
                    if (args.length) {
                        var _global;
                        return (_global = global)[key2].apply(_global, [
                            options
                        ].concat(_to_consumable_array(args)));
                    }
                    return global[key2](options);
                }
                if (config.transformMeta) {
                    var transformResult = config.transformMeta(key2, options);
                    key2 = transformResult.key;
                    options = transformResult.options;
                    if (!global.hasOwnProperty(key2)) {
                        return nonsupport(key2)();
                    }
                }
                var task = null;
                var obj = Object.assign({}, options);
                setUniqueKeyToRoute(key2, options);
                var p = new Promise(function(resolve, reject) {
                    obj.success = function(res) {
                        var _a, _b;
                        (_a = config.modifyAsyncResult) === null || _a === void 0 ? void 0 : _a.call(config, key2, res);
                        (_b = options.success) === null || _b === void 0 ? void 0 : _b.call(options, res);
                        if (key2 === "connectSocket") {
                            resolve(Promise.resolve().then(function() {
                                return task ? Object.assign(task, res) : res;
                            }));
                        } else {
                            resolve(res);
                        }
                    };
                    obj.fail = function(res) {
                        var _a;
                        (_a = options.fail) === null || _a === void 0 ? void 0 : _a.call(options, res);
                        reject(res);
                    };
                    obj.complete = function(res) {
                        var _a;
                        (_a = options.complete) === null || _a === void 0 ? void 0 : _a.call(options, res);
                    };
                    if (args.length) {
                        var _global;
                        task = (_global = global)[key2].apply(_global, [
                            obj
                        ].concat(_to_consumable_array(args)));
                    } else {
                        task = global[key2](obj);
                    }
                });
                if ([
                    "uploadFile",
                    "downloadFile"
                ].includes(key2)) {
                    equipTaskMethodsIntoPromise(task, p);
                    p.progress = function(cb) {
                        task === null || task === void 0 ? void 0 : task.onProgressUpdate(cb);
                        return p;
                    };
                    p.abort = function(cb) {
                        cb === null || cb === void 0 ? void 0 : cb();
                        task === null || task === void 0 ? void 0 : task.abort();
                        return p;
                    };
                }
                return p;
            };
        } else {
            var platformKey = key;
            if (config.transformMeta) {
                platformKey = config.transformMeta(key, {}).key;
            }
            if (!global.hasOwnProperty(platformKey)) {
                taro[key] = nonsupport(key);
                return;
            }
            if (isFunction(global[key])) {
                taro[key] = function() {
                    for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){
                        args[_key] = arguments[_key];
                    }
                    if (config.handleSyncApis) {
                        return config.handleSyncApis(key, global, args);
                    } else {
                        return global[platformKey].apply(global, args);
                    }
                };
            } else {
                taro[key] = global[platformKey];
            }
        }
    });
    !config.isOnlyPromisify && equipCommonApis(taro, global, config);
}
function equipCommonApis(taro, global) {
    var apis = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};
    taro.canIUseWebp = getCanIUseWebp(taro);
    taro.getCurrentPages = getCurrentPages || nonsupport("getCurrentPages");
    taro.getApp = getApp || nonsupport("getApp");
    taro.env = global.env || {};
    try {
        taro.requirePlugin = requirePlugin || nonsupport("requirePlugin");
    } catch (error) {
        taro.requirePlugin = nonsupport("requirePlugin");
    }
    var request = apis.request || getNormalRequest(global);
    function taroInterceptor(chain) {
        return request(chain.requestParams);
    }
    var link = new taro.Link(taroInterceptor);
    taro.request = link.request.bind(link);
    taro.addInterceptor = link.addInterceptor.bind(link);
    taro.cleanInterceptors = link.cleanInterceptors.bind(link);
    taro.miniGlobal = taro.options.miniGlobal = global;
    taro.getAppInfo = function() {
        return {
            platform: "web",
            taroVersion: "3.6.34",
            designWidth: taro.config.designWidth
        };
    };
    taro.createSelectorQuery = delayRef(taro, global, "createSelectorQuery", "exec");
    taro.createIntersectionObserver = delayRef(taro, global, "createIntersectionObserver", "observe");
}
function equipTaskMethodsIntoPromise(task, promise) {
    if (!task || !promise) return;
    var taskMethods = [
        "abort",
        "onHeadersReceived",
        "offHeadersReceived",
        "onProgressUpdate",
        "offProgressUpdate",
        "onChunkReceived",
        "offChunkReceived"
    ];
    task && taskMethods.forEach(function(method) {
        if (method in task) {
            promise[method] = task[method].bind(task);
        }
    });
}
function delayRef(taro, global, name, method) {
    return function() {
        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){
            args[_key] = arguments[_key];
        }
        var _global;
        var res = (_global = global)[name].apply(_global, _to_consumable_array(args));
        var raw = res[method].bind(res);
        res[method] = function() {
            for(var _len = arguments.length, methodArgs = new Array(_len), _key = 0; _key < _len; _key++){
                methodArgs[_key] = arguments[_key];
            }
            taro.nextTick(function() {
                return raw.apply(void 0, _to_consumable_array(methodArgs));
            });
        };
        return res;
    };
}
var Shortcuts;
(function(Shortcuts2) {
    Shortcuts2["Container"] = "container";
    Shortcuts2["Childnodes"] = "cn";
    Shortcuts2["Text"] = "v";
    Shortcuts2["NodeType"] = "nt";
    Shortcuts2["NodeName"] = "nn";
    Shortcuts2["Style"] = "st";
    Shortcuts2["Class"] = "cl";
    Shortcuts2["Src"] = "src";
})(Shortcuts || (Shortcuts = {}));
export { touchEvents, animation, singleQuote, internalComponents, controlledComponent, focusComponents, voidElements, nestElements, PLATFORM_TYPE, PLATFORM_CONFIG_MAP, Events, EventChannel, isString, isUndefined, isNull, isObject, isBoolean, isFunction, isNumber, isBooleanStringLiteral, isArray, isWebPlatform, HOOK_TYPE, TaroHook, TaroHooks, hooks, EMPTY_OBJ, EMPTY_ARR, noop, box, unbox, toDashed, toCamelCase, toKebabCase, capitalize, hasOwn, ensure, warn, queryToJson, getUniqueKey, cacheDataSet, cacheDataGet, cacheDataHas, mergeInternalComponents, getComponentsAlias, getPlatformType, mergeReconciler, nonsupport, setUniqueKeyToRoute, indent, processApis, Shortcuts };
